package cgg.gov.in.apsec.asdvoter.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import cgg.gov.in.apsec.pojo.ElectionProcessRuralLocalGovtDTO;
import cgg.gov.in.apsec.repo.MptcMarkedCopyRepository;

@Service
public  class MptcMarkedCopyServiceImpl implements MptcMarkedCopyService {

	
	@Autowired
	MptcMarkedCopyRepository repo;
	
	
	@Override
	public List<Map<String, Object>> getMPTCWiseData(
			ElectionProcessRuralLocalGovtDTO electionProcessRuralLocalGovtDTO) {
		String mptcName = electionProcessRuralLocalGovtDTO.getMptcName();
	    //String filteredMptcName = mptcName.split("_")[0];
	    //Long mptcId = Long.parseLong(filteredMptcName);
		return repo.getMPTCWiseData(electionProcessRuralLocalGovtDTO.getDistrictIdMPP(),electionProcessRuralLocalGovtDTO.getMppCodeMPP(),electionProcessRuralLocalGovtDTO.getRevenueDivisionIdMPP(),mptcName);
	}


	@Override
	public Map<String, Object> getMukhapatramData(String districtId, String revenueDivisionId, String mppCode,
			String mptcId, String psCode) {
		
		return repo.getMukhapatramData(districtId,mppCode,mptcId,psCode);
	}
	
	@Override
	public  Map<String, Object> findPollingStationDetails(String districtId,String revenueDivisionId,String mandalId,String mptcId,
		     String gpcode, String psNo){
		
		return repo.findPollingStationDetails(districtId, revenueDivisionId, mandalId, mptcId, gpcode, psNo);
	}

}
