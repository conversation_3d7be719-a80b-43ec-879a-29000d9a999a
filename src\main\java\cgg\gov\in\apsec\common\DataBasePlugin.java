package cgg.gov.in.apsec.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;

public class DataBasePlugin {

	public static String success(String name) {
		String msg = "<font style=\"font-size: 16px; color: green;\">" + name + "  </font>";
		return msg;

	}

	public static String error(String name) {
		String msg = "<font style=\"font-size: 16px; color: red;\">" + name + "</font>";
		return msg;
	}

	public static String RegistrationSuccess(String name) {
		String msg = "";
		if (name.length() > 0) {
			msg = "<div class=\"alert alert-success alert-dismissible fade show\" style=\"padding: 6px;\"  role=\"alert\">"
					+ "<button type=\"button\" style=\"padding-top: 5px;\" class=\"close\" data-dismiss=\"alert\" aria-label=\"Close\"><span aria-hidden=\"true\">&times;</span></button>"
					+ "<font style=\"font-size: 16px; color: white;\">" + name + "</font>" + "</div>";
		}
		return msg;
	}

	public static ModelAndView sessionExpired() {
		ModelAndView modelAndView = new ModelAndView("logout");
		modelAndView.addObject("message", DataBasePlugin.error("Session Expired (or) User Logged Out Successfully"));
		return modelAndView;
	}

//	@SuppressWarnings("deprecation")
//	public static List<Map<String, Object>> getData(String sql, EntityManager entityManager) {
//		Query query = entityManager.createNativeQuery(sql);
//		List<Map<String, Object>> aliasToValueMapList = query.getResultList();
//		return aliasToValueMapList;
//	}

	public static ArrayList<Integer> removeDuplicateElements(ArrayList<Integer> listData,
			ArrayList<Integer> listRemove) {
		listData.removeAll(listRemove);
		return listData;
	}

	public static String getJsonData(List<Map<String, Object>> Details) {
		JSONArray result = null;
		try {
			List<Map<String, Object>> detailsData = getDashBoardData(Details);
			List<JSONObject> jsonObj = new ArrayList<JSONObject>();
			for (Map<String, Object> data : detailsData) {
				JSONObject obj = new JSONObject(data);
				jsonObj.add(obj);
			}
			result = new JSONArray(jsonObj);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result.toString();
	}

	public static List<Map<String, Object>> getDashBoardData(List<Map<String, Object>> Details) {
		List<Map<String, Object>> outerData = new ArrayList<Map<String, Object>>();
		try {
			for (Map<String, Object> data : Details) {
				for (Map.Entry<String, Object> entry : data.entrySet()) {
					HashMap<String, Object> innerData = new HashMap<>();
					innerData.put("name", "'" + entry.getKey() + "'");
					innerData.put("data", "[" + entry.getValue() + "]");
					outerData.add(innerData);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return outerData;
	}

	public static Map<Integer, Integer> getYearsList(int fromYear, int toYear) {
		Map<Integer, Integer> yearsList = new HashMap<Integer, Integer>();
		for (int s = fromYear; s <= toYear; s++) {
			yearsList.put(s, s);
		}
		return yearsList;
	}

	public static boolean nullOrBlank(Object value) {
		return (null == value) || "".equals(value);
	}

}
