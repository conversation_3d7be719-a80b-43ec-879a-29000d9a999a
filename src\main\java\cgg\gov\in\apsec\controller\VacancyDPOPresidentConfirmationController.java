package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.VacancyForPresidentDTO;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.MasterMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.AddGPWardService;
import cgg.gov.in.apsec.service.VacancyDPOPresidentConfirmationService;

@Controller
public class VacancyDPOPresidentConfirmationController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private DistrictRepo districtRepo;

	@Autowired
	private AddGPWardService gpWardSevice;

	@Autowired
	private MasterMandalRepository mastermandalrepo;

	@Autowired
	private VacancyDPOPresidentConfirmationService vacDPOPreConSer;

	@GetMapping(value = "/vacancyDPOPresidentConfirmation")
	public String vacancyDPOPresidentConfirmation(Model model,
			@ModelAttribute("VacancyForPresidentDTO") VacancyForPresidentDTO vacancyForPresidentDTO,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);
				List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
				model.addAttribute("revenueDiv", revenueDiv);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "vacancyDPOPresidentConfirmation";
	}

	@RequestMapping(value = "/getVacancyDPOPresidentConfirmationdtls", method = RequestMethod.POST)
	public String getVacancyDPOPresidentConfirmationdtls(Model model,
			@ModelAttribute("VacancyForPresidentDTO") VacancyForPresidentDTO VacancyForPresidentDTO,
			@RequestParam(name = "districtId", required = false) String districtId,
			@RequestParam(name = "revenueDivisionId", required = false) String revenueDivisionId,
			@RequestParam(name = "mandalId", required = false) String mandalId) {
		try {
			District district = districtRepo.findById(districtId).orElse(null);
			List<Map<String, Object>> vacDPOPresidentDtls = vacDPOPreConSer
					.getVacancyDPOPresidentConfirmationdtls(districtId, revenueDivisionId, mandalId);
			VacancyForPresidentDTO.setDistrictId(districtId);
			model.addAttribute("vacDPOPresidentDtls", vacDPOPresidentDtls);

			Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);

			model.addAttribute("districtData", districtData);
			model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
			model.addAttribute("districtId", districtId);
			List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
			model.addAttribute("revenueDiv", revenueDiv);
			List<Map<String, Object>> mandalsList = userRepo.findMandalBasedOnRevenueDivision(districtId,
					revenueDivisionId);
			model.addAttribute("mandalsList", mandalsList);
			model.addAttribute("selectedMandalId", mandalId);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "vacancyDPOPresidentConfirmation";
	}

	@RequestMapping(value = "/vacancyDPOPresidentConfirmationMethod", method = RequestMethod.POST)
	public String vacancyDPOPresidentConfirmationMethod(@RequestParam("id") int id,
			@RequestParam("districtId") String districtId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("mandalId") String mandalId, @RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = vacDPOPreConSer.saveVacancyDPOPresidentConfirmation(id, districtId, revenueDivisionId,
				mandalId, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details confirmed successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDPOPresidentConfirmation";
	}

	@RequestMapping(value = "/vacancyDPOPresidentRollback", method = RequestMethod.POST)
	public String vacancyDPOPresidentRollback(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("mandalId") String mandalId,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = vacDPOPreConSer.vacancyDPOPresidentRollback(id, districtId, revenueDivisionId, mandalId,
				dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details Rolledback successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to Rolledback details.");
		}
		return "redirect:/vacancyDLPOPresidentConfirmation";
	}
}
