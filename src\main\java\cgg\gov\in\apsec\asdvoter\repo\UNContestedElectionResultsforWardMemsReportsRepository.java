package cgg.gov.in.apsec.asdvoter.repo;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import cgg.gov.in.apsec.modal.User;

@Repository
public interface UNContestedElectionResultsforWardMemsReportsRepository extends JpaRepository<User, Long> {

	@Query(value = " select aa.election_code as election_code,aa.election_description as election_desc,aa.election_year as election_year from  election_notifications aa "
			+ " inner join election_notification_types bb on aa.election_code = bb.election_code "
			+ " inner join master_election_types cc on bb.election_type_code = cc.election_type_code "
			+ " where   aa.election_rural_urban = 'R'  and cc.conducting_type = 'I' "
			+ " order by election_year desc ,cc.election_name", nativeQuery = true)
	List<Map<String, Object>> getElections();

	@Query(value = "SELECT (SELECT COUNT(1) FROM master_district) AS dist_cnt, "
			+ "    (SELECT COUNT(1) FROM master_urban_ulg) AS ulg_cnt, "
			+ "    (SELECT COUNT(1) FROM master_gp_wards) AS gpcode_cnt ", nativeQuery = true)
	List<Map<String, Object>> getUNContstsXIIIWardRsltsInfo(@Param("electionCode") String electionCode);

	@Query(value = "SELECT md.district_id,district_name,COUNT(ens.slno) as no_of_sarpanch FROM master_district md"
			+ "  left join election_nominations_sarpanch ens on ens.district_id=md.district_id and  ens.election_code = :electionCode"
			+ " group by 1,2 ", nativeQuery = true)
	List<Map<String, Object>> getUncontstdWardDistNames(@Param("electionCode") String electionCode);

	@Query(value = "SELECT muug.ulg_type, ulg_code FROM master_urban_ulg muug "
			+ " LEFT JOIN master_district md ON md.district_id = muug.district_id "
			+ " AND md.district_id = CAST(:distId AS VARCHAR)", nativeQuery = true)
	List<Map<String, Object>> getunContstdUlbName(@Param("distId") Integer distId);
	
	
	@Query(value = "select no_of_wards from master_urban_ulg where district_id=:districtId and ulg_type=:ulbType and ulg_code=:ulgCode order by CAST(no_of_wards AS INT)", nativeQuery = true)
	List<Map<String,Object>>  getNoOfWardsForULB(@Param("districtId") String districtId, @Param("ulbType") String ulbType, @Param("ulgCode") String ulgCode);
}
