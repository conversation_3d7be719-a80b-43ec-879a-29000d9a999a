package cgg.gov.in.apsec.asdvoter.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.persistence.EntityNotFoundException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cgg.gov.in.apsec.asdvoter.repo.ULBWiseASDVotersRepo;
import cgg.gov.in.apsec.modal.AsdVotersWardPswise;
import cgg.gov.in.apsec.pojo.ListofAsdVotersPSWiseDTO;
import cgg.gov.in.apsec.pojo.SubDtoASDList;
import cgg.gov.in.apsec.repo.ULBWiseASDListRepository;
import cgg.gov.in.apsec.repo.ULBWiseASDRepo;

@Service
public class ULBWiseASDVotersService {

	@Autowired
	private ULBWiseASDVotersRepo ulbWiseASDVotersRepo;
	
	@Autowired
	private ULBWiseASDListRepository ulbWiseASDListRepository;
	

	@Autowired
	private ULBWiseASDRepo ULBWiseASDRepo;
	
	
	public List<Map<String, Object>> getULBWisePSNoList(String distId, String ulbType, String wardNum) {
		return ulbWiseASDVotersRepo.getPSDetailsULBType(distId, ulbType, wardNum);
	}

	public List<Map<String, Object>> getULBFromToSlNo(String ulbName, String districtId, String psNo, String ward) {
		return ulbWiseASDVotersRepo.getFromSlnoTOslNoForULBWiseVoer(ulbName, districtId, psNo, ward);
	}

	public List<Object[]> getULBWiseVoterDetails(String tableName, String distId, String ulgType, String wardNo,
			String voteringpEr,  int psNo) {
		
		
//		return ulbWiseASDVotersRepo.getULBWiseVoterDetails(tableName, distId, ulgType, wardNo, voteringpEr, gpCode,
//				psNo);
		
		return null;
	}

	public int getULBOwnerNameandDoorNoCheckExist(String district_id, String ulbName, String wardNum, String voterinwardEr,int psNo,String ulbCode) {
		return ulbWiseASDVotersRepo.isVoterDuplicateInWardPSWise(district_id, ulbName, String.valueOf(psNo), voterinwardEr, wardNum,Integer.parseInt(ulbCode));
	}

	public List<Map<String, Object>> fetchVoterDetails(List<String> paramList, String assemblyId) {
	    // Extract values from the list based on index positions
	    String districtId = paramList.get(5);
	    String ulgCode = paramList.get(4);
	    String wardNo = paramList.get(1); // wardNum
	    String fromSlno = paramList.get(6);
	    String toSlno = paramList.get(7);
	    String votinWardEP = paramList.get(3);

	    // Fetch voter details from repository
	    List<Map<String, Object>> voterDetails = ulbWiseASDListRepository.getVotersListTelugu(
	        districtId, ulgCode, wardNo, fromSlno, toSlno, votinWardEP, assemblyId
	    );

	    // Print the results in the service class
	    System.out.println("Fetched Voter Details:");
	    for (Map<String, Object> voter : voterDetails) {
	        System.out.println(voter);
	    }

	    return voterDetails;
	}

	public int insertulbASDVotersData(String userId, String districtName, String districtId, String ulbName,
			String ulbType, String mandalId, ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,String insertedIp) {
		List<SubDtoASDList> subDto = listofAsdVotersPSWiseDto.getSubDto();
		System.out.println("===> "  + listofAsdVotersPSWiseDto.toString());
		int result=0;
		for (int i = 0; i < subDto.size(); i++) {
		    SubDtoASDList sub = subDto.get(i);
		    AsdVotersWardPswise asdVoter = new AsdVotersWardPswise();
		    asdVoter.setCategory(sub.getCategory());
		    asdVoter.setDistrictId(districtId);
		    asdVoter.setDistrictName(districtName);
		    asdVoter.setInsertedBy(userId);
		    asdVoter.setInsertedIp(insertedIp);
		    asdVoter.setInsertedTime(LocalDateTime.now());
		    asdVoter.setIsActive(true);
		    asdVoter.setPollingStationNo(listofAsdVotersPSWiseDto.getPollingstation_no());
		    asdVoter.setUlbCode(Integer.parseInt(listofAsdVotersPSWiseDto.getUlgCode()));
		    asdVoter.setUlbName(ulbName);

		    if ("np".equals(ulbType)) {
		        asdVoter.setUlbType("1_Nagar Panchayat");
		    } else if ("mpl".equals(ulbType)) {
		        asdVoter.setUlbType("2_Municipality");
		    } else if ("mc".equals(ulbType)) {
		        asdVoter.setUlbType("3_Municipal Corporation");
		    } else if ("vsp".equals(ulbType)) {
		        asdVoter.setUlbType("4_VMC");
		    } else if ("vjw".equals(ulbType)) {
		        asdVoter.setUlbType("5_GVMC");
		    }
		    asdVoter.setVoterCompleteDetails(sub.getParticulars());
		    asdVoter.setVoterInWardEr(sub.getVoteringpEr());
		    asdVoter.setWardId(listofAsdVotersPSWiseDto.getWardNo());
		    ULBWiseASDRepo.save(asdVoter);
		    System.out.println("sub " + sub.toString());
		    result++;
		}

		return result;
	}

	
	public List<Map<String, Object>> getULBWiseASDVoterReport(String districtId, String wardId, String psNo, String ulbName, int ulbCode) {
        return ULBWiseASDRepo.getListOfASDVoterWardWise(districtId, wardId, psNo, ulbName, ulbCode);
    }

	public ListofAsdVotersPSWiseDTO editULBASDVoter(String psNo, String voterSlNo) {
	    Optional<AsdVotersWardPswise> optionalVoter = ULBWiseASDRepo.findByVoterWardErAndPollingStation(voterSlNo, psNo);
        ListofAsdVotersPSWiseDTO editDTODetails = new ListofAsdVotersPSWiseDTO();
        SubDtoASDList subdto= new SubDtoASDList();
	    if (optionalVoter.isPresent()) {
	        AsdVotersWardPswise voterDetails = optionalVoter.get();
	        // Create a DTO and map values from the entity
//	        editDTODetails.setSlno(voterDetails.getSlno());  
	        editDTODetails.setSlNo(voterDetails.getSlno());
	        editDTODetails.setDistrictName(voterDetails.getDistrictName());
	        editDTODetails.setUlbType(voterDetails.getUlbType());  
	        editDTODetails.setUlbName(voterDetails.getUlbName());
	        editDTODetails.setParticulars(voterDetails.getVoterCompleteDetails());
//	        editDTODetails.setUlbCode(voterDetails.getUlbCode());
	        editDTODetails.setPollingstation_no(voterDetails.getPollingStationNo());
//	        editDTODetails.setVoterInWardEr(voterDetails.getVoterInWardEr());
	        editDTODetails.setCategory(voterDetails.getCategory());
	        editDTODetails.setVoteringpEr(voterDetails.getVoterInWardEr());
//	        editDTODetails.set(voterDetails.getInitialSerialNumber());
	        editDTODetails.setParticulars(voterDetails.getVoterCompleteDetails());
//	        editDTODetails.setIsActive(voterDetails.getIsActive());
//	        editDTODetails.setInsertedBy(voterDetails.getInsertedBy());
//	        editDTODetails.setInsertedIp(voterDetails.getInsertedIp());
//	        editDTODetails.setInsertedTime(voterDetails.getInsertedTime());
	        editDTODetails.setWardNo(voterDetails.getWardId());

	    } else {
	        throw new EntityNotFoundException("Voter details not found for PS No: " + psNo + " and Voter Sl No: " + voterSlNo);
	    }
	    return editDTODetails;
	}

	public int updateASDVoter(ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto, int slNo) {
		return ULBWiseASDRepo.updateASDVoter(listofAsdVotersPSWiseDto.getWardNo(),listofAsdVotersPSWiseDto.getPollingstation_no(),listofAsdVotersPSWiseDto.getVoteringpEr()
				,listofAsdVotersPSWiseDto.getCategory(),listofAsdVotersPSWiseDto.getParticulars(),slNo);
	}
	
	public int deleteWardASDVoter(String slNo, String psNo) {
		return ULBWiseASDRepo.deleteWardASDVoter(Integer.valueOf(slNo),psNo);
	}

	public boolean existsByDistrictIdAndUlbCodeAndPollingStationNo(String districtId, int ulgCode, String wardNo,
			String pollingStationNo, String slno) {
		return ULBWiseASDRepo.existsByDistrictIdAndUlbCodeAndPollingStationNoAndWardIdAndVoterInWardEr(districtId,ulgCode,pollingStationNo,wardNo,slno);
	}




}
