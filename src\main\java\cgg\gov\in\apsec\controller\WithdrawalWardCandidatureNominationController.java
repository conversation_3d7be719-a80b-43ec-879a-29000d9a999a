package cgg.gov.in.apsec.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationDTO;
import cgg.gov.in.apsec.pojo.WithdrawalWardCandidatureNominationDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.WithdrawalWardCandidatureNominationRepository;
import cgg.gov.in.apsec.service.WithdrawalWardCandidatureNominationService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class WithdrawalWardCandidatureNominationController {

	@Autowired
	private WithdrawalWardCandidatureNominationService withdrawalWardCandidatureNominationService;
	
	@Autowired
	private UserRepository userRepo;
	
	@Autowired
	private WithdrawalWardCandidatureNominationRepository withdrawalWardCandidatureNominationRepository;


	@RequestMapping(value = "/withdrawalWardCandidatureNomination")
	public String withdrawalWardCandidatureNomination(Model model, HttpServletRequest request,
	        @ModelAttribute("withdrawalWardCandidatureNominationDto") WithdrawalWardCandidatureNominationDTO withdrawalWardCandidatureNominationDto) {
	    List<Map<String, Object>> wardwithDrawalNominationList = null;
	    List<Map<String, Object>> wardwithDrawalStatusList = null;
	    try {
	    	String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
	        String userId = request.getSession().getAttribute("loggedInUser").toString();
	        User detailsByUserId = userRepo.getDetailsByUserId(userId);
	        String districtId = detailsByUserId.getDistrict_id().toString();
	        String revenueId = detailsByUserId.getRevenueDivisionId();
	        String mandalId = detailsByUserId.getMandalId();
	        String gpcode = detailsByUserId.getGpcode();
	        model.addAttribute("districtId", districtId);
            model.addAttribute("revenueDivisionId", revenueId);
            model.addAttribute("mandalId", mandalId);
            model.addAttribute("gpCode",  gpcode);
	        User user = userRepo.getDetailsByUserId(userId);
	        List<Map<String, Object>> districts1 = userRepo.findDistricts();
	        model.addAttribute("districtListZPP", districts1);

	        if (user.getDistrict_id() == null || user.getDistrict_id().isEmpty() || user.getDistrict_id().equals("0")) {
	            List<Map<String, Object>> districts = userRepo.findDistricts();
	            model.addAttribute("districtList", districts);
	        } else {
	            model.addAttribute("userDistrictId", user.getDistrict_id());
	            String districtName = userRepo.getDistrictNameInt(Integer.parseInt(user.getDistrict_id()));
	            model.addAttribute("userDistrictName", districtName);
	            if (user.getRevenueDivisionId() == null || user.getRevenueDivisionId().equals("0")) {
	                List<Map<String, Object>> revenues = userRepo.findRevenueDivisionBasedOnDistrictInt(Integer.parseInt(user.getDistrict_id()));
	                model.addAttribute("revenueDivisionsList", revenues);
	            } else {
	                model.addAttribute("userRevenueDivisionId", user.getRevenueDivisionId());
	                String revenueDivisionNameInt = userRepo.getRevenueDivisionNameInt(Integer.parseInt(user.getRevenueDivisionId()), Integer.parseInt(user.getDistrict_id()));
	                model.addAttribute("userRevenueDivisionName", revenueDivisionNameInt);
	                if (user.getMandalId() == null || user.getMandalId().equals("0")) {
	                    List<Map<String, Object>> mandaList = userRepo.getMPPList(user.getDistrict_id(), user.getRevenueDivisionId());
	                    model.addAttribute("mppList", mandaList);
	                } else {
	                    mandalId = user.getMandalId();
	                    String mandalName = userRepo.getMandalNameInt(Integer.parseInt(user.getDistrict_id()), Integer.parseInt(user.getRevenueDivisionId()), Integer.parseInt(mandalId));
	                    model.addAttribute("userMppId", mandalId);
	                    model.addAttribute("userMppName", mandalName);
	                    if (user.getGpcode() == null || user.getGpcode().equals("0") || user.getGpcode().equals("")) {
	                        List<Map<String, Object>> gpList = userRepo.gramPanchayatListGP(user.getDistrict_id(), user.getRevenueDivisionId(), mandalId);
	                        model.addAttribute("gpList", gpList);
	                    } else {
	                        model.addAttribute("roGpCode", user.getGpcode());
	                        model.addAttribute("roGpName", userRepo.getGPName(user.getDistrict_id(),
	                                user.getRevenueDivisionId(), mandalId, user.getGpcode()));
	                        List<Map<String, Object>> wards = userRepo.getWardsNo(Integer.parseInt(user.getDistrict_id()), Integer.parseInt(user.getMandalId()),
	                                Integer.parseInt(user.getGpcode()));
	                        model.addAttribute("wardNos", wards);
	                    }
	                }
	            }
	        }

	        String wardNo = (String) model.asMap().get("wardNo");
	        withdrawalWardCandidatureNominationDto.setWardNo(wardNo);
	        if (wardNo == null || wardNo.equals("0")) {
	           // wardwithDrawalNominationList = withdrawalWardCandidatureNominationService.getwithdrawalNominationDataList(districtId, revenueId, mandalId, gpcode);
	           // wardwithDrawalStatusList = withdrawalWardCandidatureNominationService.getwardwithDrawaStatusList(districtId, mandalId, gpcode);
	        } else {
	            wardwithDrawalNominationList = withdrawalWardCandidatureNominationService.getwithdrawalNominationDataListWard(districtId, revenueId, mandalId, gpcode, wardNo);
	            wardwithDrawalStatusList = withdrawalWardCandidatureNominationService.getwardwithDrawaStatusListWard(districtId, mandalId, gpcode, wardNo);
	            model.addAttribute("slectedWardNo", wardNo);
	            model.addAttribute("wardwithDrawalNominationList", wardwithDrawalNominationList);
		        model.addAttribute("wardwithDrawalStatusList", wardwithDrawalStatusList);
	        }

	       

	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return "withdrawalWardCandidatureNomination";
	}

	@PostMapping("/getDetailsForGpWardWithDrawl")
	public String getDetailsForGpWardWithDrawl(RedirectAttributes redirectAttr,
	        @ModelAttribute("withdrawalWardCandidatureNominationDto") WithdrawalWardCandidatureNominationDTO withdrawalWardCandidatureNominationDto, HttpServletRequest request) throws Exception {
	    try {
	    	
	    	String userId = (String) request.getSession().getAttribute("loggedInUser");
			User user = userRepo.getDetailsByUserId(userId);
			if(user.getDistrict_id()!=null && !user.getDistrict_id().isEmpty() && !"0".equals(user.getDistrict_id()) )
			{
				if(!withdrawalWardCandidatureNominationDto.getDistrictId().equals(user.getDistrict_id())) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			}
			if(user.getMandalId()!=null && !user.getMandalId().isEmpty()  && !"0".equals(user.getMandalId()) )
			{
				if(!withdrawalWardCandidatureNominationDto.getMandalId().equals(user.getMandalId()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			} 
			if(user.getRevenueDivisionId()!=null && !user.getRevenueDivisionId().isEmpty() &&  !"0".equals(user.getRevenueDivisionId()))
			{
				if(!withdrawalWardCandidatureNominationDto.getRevenueDivisionId().equals(user.getRevenueDivisionId()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			} 
			
			if(user.getGpcode()!=null && !user.getGpcode().isEmpty() &&  !"0".equals(user.getGpcode()))
			{
				if(!withdrawalWardCandidatureNominationDto.getGpcode().equals(user.getGpcode()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			} 
	        redirectAttr.addFlashAttribute("wardNo", withdrawalWardCandidatureNominationDto.getWardNo());
	    } catch(Exception e) {
			
			if (e instanceof AccessDeniedException) {
				throw e;
			}
			e.printStackTrace();
		} 
	    return "redirect:/withdrawalWardCandidatureNomination";
	}

	/*@PostMapping("wardwithdrawnStatus")
	public String wardwithdrawnStatus(RedirectAttributes redirectAttr, HttpServletRequest request,
			@ModelAttribute("withdrawalWardCandidatureNominationDto") WithdrawalWardCandidatureNominationDTO withdrawalWardCandidatureNominationDto,
			@RequestParam("slnoList") String slnoList) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
					new TypeReference<List<Map<String, String>>>() {
					});
			int result = withdrawalWardCandidatureNominationService.updateWardWithdrawalNomination(slnoListMap);
			if (result > 0) {
				redirectAttr.addFlashAttribute("updateSuccess", " With Drawn Has Done");
			} else {
				redirectAttr.addFlashAttribute("updateFailure", " Failed to With Drawn ");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/withdrawalWardCandidatureNomination";
	}*/
	
	/*@PostMapping("wardwithdrawnStatus")
	public String withdrawnStatus(
	        RedirectAttributes redirectAttr,
	        @ModelAttribute("withdrawalCandidatureNominationDto") WithdrawalCandidatureNominationDTO withdrawalCandidatureNominationDto,
	        @RequestParam("slnoList") String slnoList,
	        @RequestParam("district_Id") String districtId,
	        @RequestParam("revenueDivision_Id") String revenueDivisionId,
	        @RequestParam("mandal_Id") String mandalId,
	        @RequestParam("gp_Code") String grampanchayatId,
	        @RequestParam("wardId") String wardNo,
	        @RequestParam("processComplete") int processComplete) {
	    try {
	    	
	    	ObjectMapper objectMapper = new ObjectMapper();
			List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
					new TypeReference<List<Map<String, String>>>() {
					});
			int result = withdrawalWardCandidatureNominationService.updateWardWithdrawalNomination(slnoListMap);
			if (result > 0) {
				redirectAttr.addFlashAttribute("updateSuccess", " With Drawn Has Done");
			} else {
				redirectAttr.addFlashAttribute("updateFailure", " Failed to With Drawn ");
			}
	        int existingCount = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmCountAjax(
	                districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo);
	        if (existingCount > 0) {
	            redirectAttr.addFlashAttribute("updateFailure", "Data already confirmed!");
	            return "redirect:/withdrawalWardCandidatureNomination";
	        }

	        int roCount = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmByROAjax(
	                districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo,processComplete);
	        if (roCount > 0) {
	            return "redirect:/withdrawalWardCandidatureNomination";
	        }

	       
	    } catch (Exception e) {
	        e.printStackTrace();
	        redirectAttr.addFlashAttribute("updateFailure", "An error occurred during processing.");
	    }
	    return "redirect:/withdrawalWardCandidatureNomination";
	}
*/
	@PostMapping("wardwithdrawnStatus")
	@ResponseBody
	public ResponseEntity<Map<String, Object>> withdrawnStatus(
	        @ModelAttribute("withdrawalCandidatureNominationDto") WithdrawalCandidatureNominationDTO withdrawalCandidatureNominationDto,
	        @RequestParam("slnoList") String slnoList,
	        @RequestParam("district_Id") String districtId,
	        @RequestParam("revenueDivision_Id") String revenueDivisionId,
	        @RequestParam("mandal_Id") String mandalId,
	        @RequestParam("gp_Code") String grampanchayatId,
	        @RequestParam("wardId") String wardNo,
	        @RequestParam("processComplete") int processComplete) {

	    Map<String, Object> response = new HashMap<>();
	    try {
	        ObjectMapper objectMapper = new ObjectMapper();
	        List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
	                new TypeReference<List<Map<String, String>>>() {});

	        // Update withdrawal nominations
	        int result = withdrawalWardCandidatureNominationService.updateWardWithdrawalNomination(slnoListMap);
	        if (result > 0) {
	            response.put("success", true);
	            response.put("message", "Withdrawal process completed successfully.");
	        } else {
	            response.put("success", false);
	            response.put("message", "Failed to complete the withdrawal process.");
	            return ResponseEntity.ok(response);
	        }

	        // Check if data is already confirmed
	        int existingCount = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmCountAjax(
	                districtId, revenueDivisionId, mandalId, grampanchayatId, wardNo);
	        if (existingCount > 0) {
	            response.put("success", false);
	            response.put("message", "Data already confirmed!");
	            return ResponseEntity.ok(response);
	        }

	        // Process completion confirmation
	        int roCount = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmByROAjax(
	                districtId, revenueDivisionId, mandalId, grampanchayatId, wardNo, processComplete);
	        if (roCount > 0) {
	            response.put("success", true);
	            response.put("message", "Process completed and data updated.");
	        } else {
	            response.put("success", false);
	            response.put("message", "No data updated during process completion.");
	        }
	    } catch (Exception e) {
	        e.printStackTrace();
	        response.put("success", false);
	        response.put("message", "An error occurred during processing: " + e.getMessage());
	    }

	    return ResponseEntity.ok(response);
	}

	@RequestMapping(value = "/receiptwardAcknowledged")
	public String receiptwardAcknowledged(Model model, @RequestParam("slno") String slno) {
		try {
			model.addAttribute("slno", slno);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "receiptAcknowledgedforWard";
	}

	@RequestMapping(value = "/reportAKWWardData")
	 public ResponseEntity<String> reportACKFormWardData(Model model, 
			 @ModelAttribute("withdrawalWardCandidatureNominationDto") WithdrawalWardCandidatureNominationDTO withdrawalWardCandidatureNominationDto,            
         RedirectAttributes redirectAttr,HttpServletRequest request,
           @RequestParam("akwcandidateSigned") String akwcandidateSigned,
           @RequestParam("akwbywhomewardformSubmite") String akwbywhomewardformSubmite,
           @RequestParam("dateFieldOne") String dateFieldOne,
           @RequestParam("timeFieldTwo") String timeFieldTwo,
           @RequestParam("slno") Integer slno) {	     
	     try {
	    	 String userId = request.getSession().getAttribute("loggedInUser").toString();
	    	 WithdrawalWardCandidatureNominationDTO dto=new WithdrawalWardCandidatureNominationDTO();
	    	 dto.setAkwwardcandidateSigned(akwcandidateSigned);
	    	 dto.setAkwbywhomewardformSubmite(akwbywhomewardformSubmite);
	    	
	    	 dto.setDateFieldOne(dateFieldOne);
	    	 dto.setTimeFieldTwo(timeFieldTwo);
			int result = withdrawalWardCandidatureNominationService
					.insertreportAKWFormInfo(withdrawalWardCandidatureNominationDto, userId, slno.toString(),akwcandidateSigned,akwbywhomewardformSubmite);
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess", " ReportAcknowlwdged Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed ReportAcknowlwdged Data ...!!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	     return ResponseEntity.ok("Success");
	}

	@RequestMapping(value = "/fetchWardFormAKWInfo")
	public String fetchWardFormAKWInfo(HttpServletRequest request ,Model model, @RequestParam("slno") String slno) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			
			model.addAttribute("gpNameTel",userRepo.getGpNameTel(user.getDistrict_id(), user.getRevenueDivisionId(),
					user.getMandalId(), user.getGpcode()));
			
			model.addAttribute("wardgpcodeName",userRepo.getWard(slno));
			String  akwwardCandidateName =  withdrawalWardCandidatureNominationService.akwWardName(slno);
	    	model.addAttribute("akwwardCandidateName", akwwardCandidateName);
	    	
	    	String  akwcandidateDate =  withdrawalWardCandidatureNominationService.akwDate(slno);
	    	model.addAttribute("akwcandidateDate", akwcandidateDate);
	    	
	    	String  akwcandidateTime =  withdrawalWardCandidatureNominationService.akwTime(slno);
	    	model.addAttribute("akwcandidateTime", akwcandidateTime);
	    	
	    	String akwbywhomeformSubmit = withdrawalWardCandidatureNominationService.akwbywhomeformSubmit(slno);
			model.addAttribute("akwbywhomeformSubmit", akwbywhomeformSubmit);
            String whomSubmitted="";
			if(akwbywhomeformSubmit.equals("1")) {
				whomSubmitted=	akwwardCandidateName;
			}
			if(akwbywhomeformSubmit.equals("2")) {
				whomSubmitted=userRepo.whomSubmitted_proposer_ward(slno);
			}
			if(akwbywhomeformSubmit.equals("3")) {
				whomSubmitted=userRepo.whomSubmitted_election_agent_ward(user.getDistrict_id(),user.getMandalId(), user.getGpcode());
			}
			model.addAttribute("whomSubmitted",whomSubmitted);
	    	
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchFormforWardReportAcknowledgeTel";
	}
	
	@RequestMapping(value = "/getWithdrawlWardConfirmCountAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlConfirmCountAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "mandalId") String mandalId, @RequestParam(value = "grampanchayatId") String grampanchayatId, @RequestParam(value = "wardId") String wardNo) {
		int count = 0;
		try { 
			count = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmCountAjax(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	@RequestMapping(value = "/getWithdrawlWardConfirmByROAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlConfirmByROAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "mandalId") String mandalId, @RequestParam(value = "grampanchayatId") String grampanchayatId, @RequestParam(value = "wardId") String wardNo,
			@RequestParam(value = "processComplete") int processComplete) {
		int count = 0;
		try {
				if(processComplete == 1) {
					count = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmByROAjax(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo, processComplete);
				}else if(processComplete == 2){
					int processCount = withdrawalWardCandidatureNominationRepository.getWithdrawlProcessCount(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo);
					if(processCount > 0) {
						int updateCount = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmByROAjax(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo, processComplete);
						if(updateCount > 0) {
							count = withdrawalWardCandidatureNominationRepository.getWithdrawlDraftCount(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo);
						}	
					}
				}else if(processComplete == 3){
					int draftCount =  withdrawalWardCandidatureNominationRepository.getWithdrawlDraftCount(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo);
					if(draftCount > 0) {
						count = withdrawalWardCandidatureNominationRepository.getWithdrawlConfirmByROAjax(districtId, revenueDivisionId, mandalId, grampanchayatId,wardNo, processComplete);
					}
				}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	
}