package cgg.gov.in.apsec.asdvoter.controller;

import java.security.Principal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.asdvoter.service.ULBWiseASDVotersService;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.ListofAsdVotersPSWiseDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class ULBWiseASDVotersEntryController {

	private static Logger logger = LoggerFactory.getLogger(ULBWiseASDVotersEntryController.class);

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private ULBWiseASDVotersService ulbWiseASDVotersService;

	@GetMapping("/ulbWiseASDVoterEntry")
	public String ulbWiseADVoterEntry(
			@ModelAttribute("listofULBAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto, Model model,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String token = TokenUtil.generateToken(request.getSession());

			model.addAttribute("formToken", token);
			String districtId = detailsByUserId.getDistrict_id();
			String ulgCode = detailsByUserId.getUlgCode();
			
			String districtName = userRepo.getDistrictNameInt(Integer.parseInt(districtId));

//			String ulgName = userRepo.getULGName(districtId, ulgCode);
//			model.addAttribute("ulgName", ulgName);
			model.addAttribute("ulgCode", ulgCode);
			model.addAttribute("districtId", districtId);
			if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("")) {
			String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
			}
			List<Object[]> ulgTypeList = userRepo.getUlgTypes(districtId, ulgCode);

			String ulgType = "";
			String assemblyId = "";

			if (!ulgTypeList.isEmpty()) {
			    Object[] firstResult = ulgTypeList.get(0);
			    
			    ulgType = firstResult[0] != null ? firstResult[0].toString() : "";
			    assemblyId = firstResult[1] != null ? firstResult[1].toString() : "";
			}
			model.addAttribute("assemblyId", assemblyId);
			if (ulgCode == null) {
				List<Map<String, Object>> ulbType = userRepo.getUlbType();
				model.addAttribute("ulbTypes", ulbType);
			}
			model.addAttribute("districtName", districtName);
			model.addAttribute("ulgType", ulgType);
			int ulbWards = userRepo.getUlgWards(detailsByUserId.getDistrict_id(), ulgType,
					detailsByUserId.getUlgCode());

			List<Integer> wardsList = IntStream.rangeClosed(1, ulbWards).boxed().collect(Collectors.toList());
			
			String[] split = userId.split("_");
			String ulbType = split[0];
			String userName = detailsByUserId.getUsername();
			System.out.println("===> "  + userName);
			String[] uSplit = null;
			if (userName.contains(" ")) {
			    // Split by space if the username contains a space
			    uSplit = userName.split(" ");  // Split by space
			} else if (userName.contains("_")) {
			    // Split by underscore if the username contains an underscore
			    uSplit = userName.split("_");
			}
			String ulbName = uSplit[1];
			System.out.println("=====>" + ulbName);
			listofAsdVotersPSWiseDto.setUlbName(ulbName);
			listofAsdVotersPSWiseDto.setUlbType(ulbType);
			model.addAttribute("listofULBAsdVotersPSWiseDto", listofAsdVotersPSWiseDto);
			model.addAttribute("wardsList", wardsList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "ulbWiseASDVotersEntry";
//		return "ulbWiseAsdVotersForm";
	}

	@RequestMapping(value = "getULBWisePSNoList", method = RequestMethod.GET)
	public @ResponseBody String getULBWisePSNoList(Principal principal, HttpServletRequest request,
			HttpServletResponse response, Model model, @RequestParam("ulgType") String ulgType,
			@RequestParam("wardNum") String wardNum) {
		StringBuilder psNoData = new StringBuilder();
		try {
			String userId = principal.getName();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			List<Map<String, Object>> psNoDataList = ulbWiseASDVotersService
					.getULBWisePSNoList(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(), wardNum);
			for (Map<String, Object> map : psNoDataList) {
				psNoData.append(
						"<option value='" + map.get("pollingstation_id") + "'>" + map.get("ps_no") + "</option>");
			}
		} catch (Exception e) {
			logger.error("Error at fetching the Gp Wards.." + e.getMessage());
		}
		return psNoData.toString();
	}

	
	@RequestMapping(value = "getULBFromToSlNo", method = RequestMethod.GET)
	public @ResponseBody String getULBFromToSlNo(
	        @RequestParam("ulbName") String ulbName,
	        @RequestParam("wardNum") String ward,
	        @RequestParam("psNo") String psNo,
	        HttpServletRequest request) {

	    StringBuilder resultFromToSlNo = new StringBuilder();
	    try {
	        String userId = request.getSession().getAttribute("loggedInUser").toString();
	        User detailsByUserId = userRepo.getDetailsByUserId(userId);
	        String districtId = detailsByUserId.getDistrict_id();
	        List<Map<String, Object>> psNoDataList = ulbWiseASDVotersService.getULBFromToSlNo(ulbName, districtId, psNo, ward);
	        for (Map<String, Object> map : psNoDataList) {
	        	System.out.println("from_serialno ==> "  +map.get("from_serialno"));
	        	System.out.println("to_serialno ==> "  +map.get("to_serialno"));

	            resultFromToSlNo.append(map.get("from_serialno")).append(",")
	                            .append(map.get("to_serialno"));
	        }
	    } catch (Exception e) {
	        logger.error("Error at fetching the Gp--------------- Wards.." + e.getMessage());
	    }
	    return resultFromToSlNo.toString();
	}

	@RequestMapping(value = "getULBOwnerNameandDoorNoCheckExist")
	public @ResponseBody Boolean getULBOwnerNameandDoorNoCheckExist(Principal principal,HttpServletRequest request,
	        HttpServletResponse response, Model model,@RequestParam("psNo") int psNo,@RequestParam("wardNum") String wardNo,@RequestParam("ulbName") String ulbName,@RequestParam("voterinwardEr") String voteringpEr) {
		 String userId = (String) request.getSession().getAttribute("loggedInUser");
		User detailsByUserId = userRepo.getDetailsByUserId(userId);
		String ulgCode = detailsByUserId.getUlgCode();
		int ownerNameandDoorNoCheckExist = 0;
		try {
			 ownerNameandDoorNoCheckExist = ulbWiseASDVotersService.getULBOwnerNameandDoorNoCheckExist(detailsByUserId.getDistrict_id(), ulbName, wardNo, voteringpEr, psNo,ulgCode);
			System.out.println("===>  "  +ownerNameandDoorNoCheckExist);
			 if (ownerNameandDoorNoCheckExist > 0) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
	
	
	@GetMapping("/getULBOwnerNameandDoorNo")
	public @ResponseBody String getULBOwnerNameandDoorNo( @RequestParam("assemblyId") String assemblyId,
	        @RequestParam("psNo") String psNo,
	        @RequestParam("wardNum") String wardNum,
	        @RequestParam("ulbName") String ulbName,
	        @RequestParam("voterinwardEr") String voterinwardEr,
	        HttpServletRequest request) {
		StringBuilder mainData = new StringBuilder();
	    String userId = (String) request.getSession().getAttribute("loggedInUser");
	    User detailsByUserId = userRepo.getDetailsByUserId(userId);
	    String ulbCode = detailsByUserId.getUlgCode();
	    String district_id = detailsByUserId.getDistrict_id();
	    // Create a List<String> to store all parameters
	    List<String> paramList = new ArrayList<>();
	    paramList.add(String.valueOf(psNo));
	    paramList.add(wardNum);
	    paramList.add(ulbName);
	    paramList.add(voterinwardEr);
	    paramList.add(ulbCode);
	    paramList.add(district_id);

	    // Fetch from and to slno and add them to paramList
	    List<Map<String, Object>> psNoDataList = ulbWiseASDVotersService.getULBFromToSlNo(ulbCode, district_id, psNo, wardNum);
	    for (Map<String, Object> map : psNoDataList) {
	        System.out.println("from_serialno ==> " + map.get("from_serialno"));
	        System.out.println("to_serialno ==> " + map.get("to_serialno"));

	        paramList.add(String.valueOf(map.get("from_serialno")));
	        paramList.add(String.valueOf(map.get("to_serialno")));
	    }
	    try {
	    List<Map<String, Object>> existDoorNoOwnerName = ulbWiseASDVotersService.fetchVoterDetails(paramList,assemblyId);
	    String voterDetails = existDoorNoOwnerName.stream()
				  .map(row -> {
					    // Safely retrieve each value or provide a default empty string if null
					    String voterNameTel = row.get("voternametel") != null ? row.get("voternametel").toString() : "";
					    String voterRNameTel = row.get("voterRnametel") != null ? row.get("voterRnametel").toString() : "";
					    String houseNo = row.get("houseNo") != null ? row.get("houseNo").toString() : "";
					    String serialNo = row.get("slNo") != null ? row.get("slNo").toString() : "";
					    String epicNumber = row.get("epicNumber") != null ? row.get("epicNumber").toString() : "";
					    System.out.println("epicNumber ===> " + epicNumber);
					    String gender = row.get("gender") != null ? row.get("gender").toString() : "";
					    String rlnType = row.get("rlnType") != null ? row.get("rlnType").toString() : "";
					    String age = row.get("age") != null ? row.get("age").toString() : "";
					    System.out.println("voterNameTel==" + voterNameTel);
					    // Determine the relation string based on rlnType
					    String relation;
					    switch (rlnType) {
					        case "F":
					            relation = "తండ్రి పేరు: ";
					            break;
					        case "M":
					            relation = "తల్లి పేరు: ";
					            break;
					        case "H":
					            relation = "భర్త పేరు: ";
					            break;
					        default:
					            relation = "రిలేషన్ పేరు: ";
					    }

					    // Determine gender in Telugu
					    String genderTel;
					    if ("M".equals(gender)) {
					        genderTel = "పు";
					    } else if ("F".equals(gender)) {
					        genderTel = "స్త్రీ";
					    } else {
					        genderTel = "అనిర్దిష్టమైన లింగం";
					    }

					    // Format and return the string with the respective details
					    return String.format(
					        "వరుస సంఖ్య: %s, " +
					        "ఓటరు పేరు: %s, %s%s, " +
					        "వయస్సు: %s, " +
					        "లింగము: %s, " +
					        "ఇంటి నెం.: %s, " +
					        "ఓటరు ఐ.డి. కార్డు నెం.: %s",
					        voterinwardEr, voterNameTel, relation, voterRNameTel, age, genderTel, houseNo, epicNumber
					    );
					})


		            // Join each formatted string with a " | " separator
		            .collect(Collectors.joining(" | "));
	    System.out.println("==> " + mainData);
		if (!voterDetails.isEmpty()) {
			mainData.append(voterDetails);
		} else {
			mainData.append("No Details found for Sl No : " + voterinwardEr);
		}
	    }
		catch (Exception e) {
			e.printStackTrace();
		}
		return mainData.toString();
	} 
	
	@PostMapping("/insertulbASDVotersData")
	public String insertulbASDVotersData(
			@ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
			RedirectAttributes redirectAttr, HttpServletRequest request) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String district_id = detailsByUserId.getDistrict_id();
			String mandalId = detailsByUserId.getMandalId();
			String[] split = userId.split("_");
			String ulbType = split[0];
			String userName = detailsByUserId.getUsername();
			 
			
			System.out.println("===> "  + userName);
			String[] uSplit = null;
			if (userName.contains(" ")) {
			    // Split by space if the username contains a space
			    uSplit = userName.split(" ");  // Split by space
			} else if (userName.contains("_")) {
			    // Split by underscore if the username contains an underscore
			    uSplit = userName.split("_");
			}
			String ulbName = uSplit[1];
			String districtName = userRepo.getDistrictName(district_id);
			listofAsdVotersPSWiseDto.setDistrictName(districtName);
			listofAsdVotersPSWiseDto.setUlgCode(detailsByUserId.getUlgCode());
			int result = ulbWiseASDVotersService.insertulbASDVotersData(userId, districtName,district_id,ulbName, ulbType, mandalId,
					listofAsdVotersPSWiseDto,request.getRemoteAddr());
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess",
						" List of ASD Voters for ULB Wise Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed List of ULB Voters for PS Wise data ...!!!");
			}
		} catch (Exception e) {
			if (e instanceof AccessDeniedException) { 
                throw e;
            }
			e.printStackTrace();
		}
		return "redirect:/ulbWiseASDVoterEntry";

	}

	@GetMapping(value = "/fetchListofASDVotersulbWise")
	public String fetchListofASDVotersPSWise(HttpServletRequest request,
	        @ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
	        Model model) {
	    
	    String userId = (String) request.getSession().getAttribute("loggedInUser");
	    if (userId == null) {
	        return "redirect:/login";
	    }

	    User detailsByUserId = userRepo.getDetailsByUserId(userId);
	    String token = TokenUtil.generateToken(request.getSession());

	    model.addAttribute("formToken", token);
	    String districtId = detailsByUserId.getDistrict_id();
	    String ulgCode = detailsByUserId.getUlgCode();
	    String districtName = userRepo.getDistrictNameInt(Integer.parseInt(districtId));
	    String ulgName = userRepo.getULGName(districtId, ulgCode);
	    
	    model.addAttribute("ulgName", ulgName);
	    model.addAttribute("ulgCode", ulgCode);
	    model.addAttribute("districtId", districtId);
	    model.addAttribute("districtName", districtName);

	    String ulgType = getUlgType(districtId, ulgCode);
	    model.addAttribute("ulgType", ulgType);

	    if (ulgCode == null) {
	        List<Map<String, Object>> ulbType = userRepo.getUlbType();
	        model.addAttribute("ulbTypes", ulbType);
	    }

	    int ulbWards = userRepo.getUlgWards(districtId, ulgType, ulgCode);
	    List<Integer> wardsList = IntStream.rangeClosed(1, ulbWards).boxed().collect(Collectors.toList());
	    model.addAttribute("wardsList", wardsList);

	    String[] split = userId.split("_");
	    String ulbType = (split.length > 0) ? split[0] : "";

	    String userName = detailsByUserId.getUsername();
	    String[] uSplit = userName.contains(" ") ? userName.split(" ") : userName.split("_");
	    String ulbName = (uSplit.length > 1) ? uSplit[1] : "";

	    listofAsdVotersPSWiseDto.setUlbName(ulbName);
	    listofAsdVotersPSWiseDto.setUlbType(ulbType);
	    model.addAttribute("listofULBAsdVotersPSWiseDto", listofAsdVotersPSWiseDto);
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("")) {
			String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            
			}
	    
	    return "fetchListofASDVotersULBWise"; // Ensure consistent naming
	}

	@RequestMapping(value = "/getULBWiseASDVoterReport")
	public String getULBWiseASDVoterReport(HttpServletRequest request,
	        @ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
	        Model model) {    

	    String userId = (String) request.getSession().getAttribute("loggedInUser");
	    if (userId == null) {
	        return "redirect:/login";
	    }

	    User detailsByUserId = userRepo.getDetailsByUserId(userId);
	    String token = TokenUtil.generateToken(request.getSession());

	    model.addAttribute("formToken", token);
	    String districtId = detailsByUserId.getDistrict_id();
	    String ulgCode = detailsByUserId.getUlgCode();

	    String userName = detailsByUserId.getUsername();
	    String[] uSplit = userName.contains(" ") ? userName.split(" ") : userName.split("_");
	    String ulbName = (uSplit.length > 1) ? uSplit[1] : "";

	    int ulbCode = 0;
	    try {
	        ulbCode = Integer.parseInt(ulgCode);
	    } catch (NumberFormatException e) {
	        System.err.println("Invalid ulgCode format: " + ulgCode);
	    }
	    model.addAttribute("listofULBAsdVotersPSWiseDto", listofAsdVotersPSWiseDto);

	    List<Map<String, Object>> ulbVotersList = ulbWiseASDVotersService.getULBWiseASDVoterReport(districtId,
	            listofAsdVotersPSWiseDto.getWardNo(), listofAsdVotersPSWiseDto.getPollingstation_no(), ulbName, ulbCode);

	    String ulgType = getUlgType(districtId, ulgCode);
	    int ulbWards = userRepo.getUlgWards(districtId, ulgType, ulgCode);
	    List<Integer> wardsList = IntStream.rangeClosed(1, ulbWards).boxed().collect(Collectors.toList());

	    model.addAttribute("report", "report");
	    model.addAttribute("ulbVotersList", ulbVotersList);
	    model.addAttribute("wardsList", wardsList);
	    List<Map<String, Object>> psNoDataList = ulbWiseASDVotersService
				.getULBWisePSNoList(detailsByUserId.getDistrict_id(), ulbName, listofAsdVotersPSWiseDto.getWardNo());
	    System.out.println("=== "  + psNoDataList.size());
	    model.addAttribute("psNoData", psNoDataList);
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("")) {
			String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            
			}
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("") 
	    		&& (userId.split("_")[0].equals("ro") || userId.split("_")[0].equals("eo"))){
	    	String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            model.addAttribute("roRole","roRole");
	    }
	   
	    	model.addAttribute("ulgType", ulgType);
	    
	    return "fetchListofASDVotersULBWise"; // Ensure consistent naming
	}

	private String getUlgType(String districtId, String ulgCode) {
	    List<Object[]> ulgTypeList = userRepo.getUlgTypes(districtId, ulgCode);
	    return (!ulgTypeList.isEmpty() && ulgTypeList.get(0)[0] != null) ? ulgTypeList.get(0)[0].toString() : "";
	}

	@RequestMapping("/editULBASDVoter")
	public String editULBASDVoter(@RequestParam("psNo") String psNo,
	                              @RequestParam("voterSlNo") String voterSlNo,
	                              @ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
	                              RedirectAttributes redirectAttributes,
	                              HttpServletRequest request, Model model) {
	    String userId = (String) request.getSession().getAttribute("loggedInUser");
	    if (userId == null) {
	        return "redirect:/login";
	    }
	    try {
	        ListofAsdVotersPSWiseDTO editULBASDVoter = ulbWiseASDVotersService.editULBASDVoter(psNo, voterSlNo);
	        model.addAttribute("pollingNum", editULBASDVoter.getPollingstation_no());
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String token = TokenUtil.generateToken(request.getSession());

			model.addAttribute("formToken", token);
			String district_id = detailsByUserId.getDistrict_id();
			String mandalId = detailsByUserId.getMandalId();
			String[] split = userId.split("_");
			String ulbType = split[0];
			String userName = detailsByUserId.getUsername();
			System.out.println("===> "  + userName);
			String[] uSplit = null;
			if (userName.contains(" ")) {
			    // Split by space if the username contains a space
			    uSplit = userName.split(" ");  // Split by space
			} else if (userName.contains("_")) {
			    // Split by underscore if the username contains an underscore
			    uSplit = userName.split("_");
			}
			String ulbName = uSplit[1];
			List<Object[]> ulgTypeList = userRepo.getUlgTypes(district_id, detailsByUserId.getUlgCode());
			String ulgType = "";
			String assemblyId = "";
			if (!ulgTypeList.isEmpty()) {
			    Object[] firstResult = ulgTypeList.get(0);
			    
			    ulgType = firstResult[0] != null ? firstResult[0].toString() : "";
			    assemblyId = firstResult[1] != null ? firstResult[1].toString() : "";

			}
			model.addAttribute("assemblyId", assemblyId);
			int ulbWards = userRepo.getUlgWards(detailsByUserId.getDistrict_id(), ulgType,
					detailsByUserId.getUlgCode());
		    List<Integer> wardsList = IntStream.rangeClosed(1, ulbWards).boxed().collect(Collectors.toList());
		    List<Map<String, Object>> psNoDataList = ulbWiseASDVotersService
					.getULBWisePSNoList(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(), editULBASDVoter.getWardNo());
		    System.out.println("=== "  + psNoDataList.size());
		    model.addAttribute("psNoData", psNoDataList);
		    model.addAttribute("wardsList", wardsList);
		    editULBASDVoter.setUlbType(ulbType);
		    editULBASDVoter.setUlbName(ulbName);
	        model.addAttribute("asdVoter", editULBASDVoter);
	    } catch (Exception e) {
	        redirectAttributes.addFlashAttribute("errorMessage", "Failed to update voter record.");
	        e.printStackTrace();
	    }

	    return "asdULBVoterEditor";
	}

	@PostMapping("/updateWardASDVoterData")
	public String updateWardASDVoterData(@ModelAttribute("asdVoter") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
			RedirectAttributes redirectAttr, HttpServletRequest request) {
		
		 
		System.out.println("listofAsdVotersPSWiseDto ->  "  +  listofAsdVotersPSWiseDto.toString());
		int updatedRows = ulbWiseASDVotersService.updateASDVoter(listofAsdVotersPSWiseDto, Integer.parseInt(request.getParameter("slNo")));
		if (updatedRows > 0) {
			redirectAttr.addFlashAttribute("saveSuccess", "Voter details updated successfully  ") ;
		} else {
			redirectAttr.addFlashAttribute("saveFailure", "Failed to update of ASD Voter details ");
		}	
		return "redirect:/fetchListofASDVotersulbWiseWard";
	}
	
	
	@RequestMapping(value = "/deleteWardASDVoter")
	public String deleteWardASDVoter(HttpServletRequest request,
			@ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto, Model model,
			RedirectAttributes redirectAttr) {
		String slNo = request.getParameter("voterSlNo");
		String psNo = request.getParameter("psNo");
		int updatedRows = ulbWiseASDVotersService.deleteWardASDVoter(slNo, psNo);
		redirectAttr.addFlashAttribute("listofAsdVotersPSWiseDto",listofAsdVotersPSWiseDto);
		if (updatedRows > 0) {
			redirectAttr.addFlashAttribute("saveSuccess", "Voter details deleted successfully  ");
		} else {
			redirectAttr.addFlashAttribute("saveFailure", "Failed to deleted of ASD Voter details ");
		}
		return "redirect:/getULBWiseASDVoterReport";
	}
	
	@GetMapping(value = "/fetchListofASDVotersulbWiseWard")
	public String fetchListofASDVotersulbWiseWard(HttpServletRequest request,
	        @ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
	        Model model) {
	    
	    String userId = (String) request.getSession().getAttribute("loggedInUser");
	    if (userId == null) {
	        return "redirect:/login";
	    }
	    String token = TokenUtil.generateToken(request.getSession());
	    model.addAttribute("formToken", token);
	    User detailsByUserId = userRepo.getDetailsByUserId(userId);
	    String districtId = detailsByUserId.getDistrict_id();
	    String ulgCode = detailsByUserId.getUlgCode();
	    String districtName = userRepo.getDistrictNameInt(Integer.parseInt(districtId));
	    String ulgName = userRepo.getULGName(districtId, ulgCode);
	    
	    model.addAttribute("ulgName", ulgName);
	    model.addAttribute("ulgCode", ulgCode);
	    model.addAttribute("districtId", districtId);
	    model.addAttribute("districtName", districtName);

	    String ulgType = getUlgType(districtId, ulgCode);
	    model.addAttribute("ulgType", ulgType);

	    if (ulgCode == null) {
	        List<Map<String, Object>> ulbType = userRepo.getUlbType();
	        model.addAttribute("ulbTypes", ulbType);
	    }

	    int ulbWards = userRepo.getUlgWards(districtId, ulgType, ulgCode);
	    List<Integer> wardsList = IntStream.rangeClosed(1, ulbWards).boxed().collect(Collectors.toList());
	    model.addAttribute("wardsList", wardsList);

	    String[] split = userId.split("_");
	    String ulbType = (split.length > 0) ? split[0] : "";

	    String userName = detailsByUserId.getUsername();
	    String[] uSplit = userName.contains(" ") ? userName.split(" ") : userName.split("_");
	    String ulbName = (uSplit.length > 1) ? uSplit[1] : "";

	    listofAsdVotersPSWiseDto.setUlbName(ulbName);
	    listofAsdVotersPSWiseDto.setUlbType(ulbType);
	    model.addAttribute("listofULBAsdVotersPSWiseDto", listofAsdVotersPSWiseDto);
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("")) {
			String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            
			}
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("") 
	    		&& (userId.split("_")[0].equals("ro") || userId.split("_")[0].equals("eo"))){
	    	String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            model.addAttribute("roRole","roRole");
	    }
	    return "fetchListofASDVotersULBWise"; // Ensure consistent naming
	}
	

    @GetMapping("/checkDuplicateASD")
    public ResponseEntity<Boolean> checkDuplicateEntry(
            @RequestParam String wardNo,
            @RequestParam String pollingStationNo,@RequestParam("slno") String slno,HttpServletRequest request) {
    	boolean exists=false;
    	try {
    	  String userId = (String) request.getSession().getAttribute("loggedInUser");
  	   

  	    User detailsByUserId = userRepo.getDetailsByUserId(userId);
         exists = ulbWiseASDVotersService.existsByDistrictIdAndUlbCodeAndPollingStationNo(
        		detailsByUserId.getDistrict_id(), Integer.parseInt(detailsByUserId.getUlgCode()),wardNo
        		, pollingStationNo,slno);
        
    	}
    	catch(Exception e) {
    		e.printStackTrace();
    	}
    	return ResponseEntity.ok(exists);
    }
    
    @GetMapping(value = "/fetchListofASDVotersulbWiseWardWard")
	public String fetchListofASDVotersulbWiseWardWard(HttpServletRequest request,
	        @ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
	        Model model) {
	    
	    String userId = (String) request.getSession().getAttribute("loggedInUser");
	    if (userId == null) {
	        return "redirect:/login";
	    }

	    User detailsByUserId = userRepo.getDetailsByUserId(userId);
	    String token = TokenUtil.generateToken(request.getSession());

	    model.addAttribute("formToken", token);
	    String districtId = detailsByUserId.getDistrict_id();
	    String ulgCode = detailsByUserId.getUlgCode();
	    String districtName = userRepo.getDistrictNameInt(Integer.parseInt(districtId));
	    String ulgName = userRepo.getULGName(districtId, ulgCode);
	    
	    model.addAttribute("ulgName", ulgName);
	    model.addAttribute("ulgCode", ulgCode);
	    model.addAttribute("districtId", districtId);
	    model.addAttribute("districtName", districtName);

	    String ulgType = getUlgType(districtId, ulgCode);
	    model.addAttribute("ulgType", ulgType);

	    if (ulgCode == null) {
	        List<Map<String, Object>> ulbType = userRepo.getUlbType();
	        model.addAttribute("ulbTypes", ulbType);
	    }

	    int ulbWards = userRepo.getUlgWards(districtId, ulgType, ulgCode);
	    List<Integer> wardsList = IntStream.rangeClosed(1, ulbWards).boxed().collect(Collectors.toList());
	    model.addAttribute("wardsList", wardsList);

	    String[] split = userId.split("_");
	    String ulbType = (split.length > 0) ? split[0] : "";

	    String userName = detailsByUserId.getUsername();
	    String[] uSplit = userName.contains(" ") ? userName.split(" ") : userName.split("_");
	    String ulbName = (uSplit.length > 1) ? uSplit[1] : "";

	    listofAsdVotersPSWiseDto.setUlbName(ulbName);
	    listofAsdVotersPSWiseDto.setUlbType(ulbType);
	    model.addAttribute("listofULBAsdVotersPSWiseDto", listofAsdVotersPSWiseDto);
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("")) {
			String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            
			}
	    if(detailsByUserId.getWardId()!=null && !detailsByUserId.getWardId().equals("") 
	    		&& (userId.split("_")[0].equals("ro") || userId.split("_")[0].equals("eo"))){
	    	String wardId = detailsByUserId.getWardId();
            model.addAttribute("wardId",wardId);
            model.addAttribute("roRole","roRole");
	    }
	    return "fetchListofASDVotersULBWise"; // Ensure consistent naming
	}
}
