package cgg.gov.in.apsec.asdvoter.controller;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.asdvoter.service.AdministrativeUnitsULBService;
import cgg.gov.in.apsec.pojo.UrbanLocalGovernmentElectionProcessDTO;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class AdministrativeUnitsULBController {

	private static Logger logger = LoggerFactory.getLogger(AdministrativeUnitsULBController.class);

	@Autowired
	private AdministrativeUnitsULBService service;

	@Autowired
	private UserRepository userRepo;
	
	@Autowired
	private DistrictRepo districtRepo;
	
//	@Autowired
//	private ElectionProcessAdministrativeUnitsService electionProcessAdministrativeUnitsService;

	@GetMapping("/administrativeUnitsUlb")
	public String administrativeUnitsUlb(
			@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO urbanLocalGovernmentDTO,
			Model model,HttpServletRequest request) {
		logger.info("Entered into administrativeUnitsUlb() method..!!");
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
			model.addAttribute("ulbTypeList", ulbTypeList);

			List<Map<String, Object>> districts = userRepo.findDistricts();
			model.addAttribute("districtList", districts);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "administrativeUnitsUlb";
	}

	@PostMapping("/saveAdmUnitsUlb")
	public String saveAdmUnitsUlb(@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO dto,
			RedirectAttributes redirectAttr, HttpServletRequest request, HttpServletResponse response) {
		logger.info("Entered into administrativeUnitsUlb() method..!!");
		try {
			
			int saveUlb = service.saveUlb(dto, request.getRemoteAddr(),
					request.getSession().getAttribute("loggedInUser").toString());
			if (saveUlb > 0) {
				redirectAttr.addFlashAttribute("msg", "Successfully Saved...!!!");
			}
		} catch (Exception iae) {
	           iae.printStackTrace();
	           if (iae instanceof IllegalArgumentException) {
	        	   logger.error("Validation error while saving " + iae.getMessage());
	               redirectAttr.addFlashAttribute("saveFailure", iae.getMessage());
	               return "redirect:/administrativeUnitsUlb"; // Early return on validation failure
				
	           }
	        }

		return "redirect:/administrativeUnitsUlb";
	}

	@GetMapping("/checkUlbCodeExists")
	@ResponseBody
	public int checkUlbCodeExists(@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO dto,
			RedirectAttributes redirectAttr, HttpServletRequest request, HttpServletResponse response,
			@RequestParam("districtId") String districtId,
			@RequestParam("ulbCode") String ulbCode) {
		logger.info("Entered into checkUlbCodeExists() method..!!");
		int checkAdmUnitsExisted = 0;
		try {
			dto.setDistrictId(districtId);
			dto.setUlgCode(ulbCode);
			checkAdmUnitsExisted = service.checkAdmUnitsExisted(dto);
			return checkAdmUnitsExisted;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return checkAdmUnitsExisted;
	}
	
	@GetMapping("/getReportOfAdministrativeUnitsUlb")
	public String getReportOfAdministrativeUnitsUlb(
			@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO urbanLocalGovernmentDTO,
			Model model,HttpServletRequest request) {
		logger.info("Entered into administrativeUnitsUlb() method..!!");
		try {
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String districtId = (String) request.getSession().getAttribute("district_id");
			int districtIdInt = Integer.parseInt(districtId);
			
			List<Map<String,Object>> report=service.getReportOfAdministrativeUnitsUlb();
			model.addAttribute("reportData", report);
			
			model.addAttribute("now",new Timestamp(System.currentTimeMillis()));
			
			String loggedInUser = (String) request.getSession().getAttribute("loggedInUser");
			int roleId = userRepo.getDetailsByUserId(loggedInUser).getRole_id();
			if (roleId == 1) {

				List<Map<String, Object>> districts = districtRepo.getDistrictsList();
				model.addAttribute("districts", districts);
				
				List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
				model.addAttribute("ulbTypeList", ulbTypeList);
			
			} else {
				
				String districtName = districtRepo.getDistrictName(districtIdInt);
				model.addAttribute("districtId", districtId);
				model.addAttribute("districtName", districtName);
			
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "administrativeUnitsUlbReport";
	}
	@PostMapping("/getUlbAdmUnitsReportNew")
	public String getUlbAdmUnitsReportNew(
			@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO dto,
			RedirectAttributes redirectAttr,Model model,HttpServletRequest request) {
		logger.info("Entered into getReportOfAdministrativeUnits() method..!!");
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String districtId = (String) request.getSession().getAttribute("district_id");
			int districtIdInt = Integer.parseInt(districtId);
			
	        List<Map<String, Object>> report;
	        if ("0".equals(dto.getDistrictId()) && "0".equals(dto.getUlgType())) {
	            report = service.getReportOfAdministrativeUnitsUlb();
	        } else {
	            report = service.getDistrictUlbReportOfAdministrativeUnitsUlb(dto.getDistrictId(), dto.getUlgType());
	        }

	        model.addAttribute("reportData", report);
			
			
			model.addAttribute("now",new Timestamp(System.currentTimeMillis()));
			
			String loggedInUser = (String) request.getSession().getAttribute("loggedInUser");
			int roleId = userRepo.getDetailsByUserId(loggedInUser).getRole_id();
			if (roleId == 1) {

				List<Map<String, Object>> districts = districtRepo.getDistrictsList();
				model.addAttribute("districts", districts);
				
				List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
				model.addAttribute("ulbTypeList", ulbTypeList);
			
			} else {
				
				String districtName = districtRepo.getDistrictName(districtIdInt);
				model.addAttribute("districtId", districtId);
				model.addAttribute("districtName", districtName);
			
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "administrativeUnitsUlbReport";
	}
	
	@PostMapping("/deleteAdmUnitsUlb")
	public String deleteAdmUnitsUlb(@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO dto,
			RedirectAttributes redirectAttr, HttpServletRequest request, HttpServletResponse response) {
		logger.info("Entered into deleteAdmUnitsUlb() method..!!");
		try {
			
			int saveUlb = service.deleteAdmUnitsUlb(dto);
			if (saveUlb > 0) {
				redirectAttr.addFlashAttribute("deleteSuccess", "Successfully Deleted...!!!");
			}
			else {
				redirectAttr.addFlashAttribute("deleteFailure","ULB Already Mapped Please check...");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getReportOfAdministrativeUnitsUlb";
	}
	
	@PostMapping("/editAdministrativeUnitsUlb")
	public String editAdministrativeUnitsUlb(HttpServletRequest request,Model model, @RequestParam("slno") Integer slno,
			@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO urbanLocalGovernmentDTO) {
		logger.info("Entered into editAdministrativeUnitsUlb() method..!!");
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
			model.addAttribute("ulbTypeList", ulbTypeList);
			Map<String, Object> administrativeUnitsForEdit = service
					.getAdministrativeUnitsForEdit(slno);
			model.addAttribute("administrativeUnitsForEdit", administrativeUnitsForEdit);
			model.addAttribute("ulbTypeSelected", administrativeUnitsForEdit.get("ulb_type"));
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "editAdministrativeUnitsUlb";
	}

	@PostMapping("/updateAdministrativeUnitsForElectionProcessUlb")
	public String updateAdministrativeUnitsForElectionProcessUlb(
			@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO urbanLocalGovernmentDTO,
			Model model,RedirectAttributes redirectAttr, @RequestParam("slno") Integer slno) {
		logger.info("Entered into updateAdministrativeUnitsForElectionProcessUlb() method..!!");
		try {
			int updateCount = service
					.updateAdministrativeUnit(urbanLocalGovernmentDTO, slno);
//			 if (updateCount == -1) {
//		            redirectAttr.addFlashAttribute("editFailure", "ULB Code already exists, update failed!");
//		        } else
		       if (updateCount > 0) {
		            redirectAttr.addFlashAttribute("editSuccess", "Administrative unit edited successfully..!!");
		        } else {
		            redirectAttr.addFlashAttribute("editFailure", "Failed to edit Administrative Unit as it exits in Urban Local Bodies Mapping  ..!!");
		        }
			List<Map<String,Object>> report=service.getReportOfAdministrativeUnitsUlb();
			model.addAttribute("reportData", report);
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof IllegalArgumentException) {
	        	   logger.error("Validation error while saving " + e.getMessage());
		            redirectAttr.addFlashAttribute("editFailure", "Failed to edit Administrative unit..!!");
					return "redirect:/getReportOfAdministrativeUnitsUlb";
				
	           }
		}
		return "redirect:/getReportOfAdministrativeUnitsUlb";
	}
	
	@GetMapping("/checkUlbCodeExistsForUlb")
	@ResponseBody
	public int checkUlbCodeExistsForUlb(@ModelAttribute("urbanLocalGovernmentDTO") UrbanLocalGovernmentElectionProcessDTO dto,
			RedirectAttributes redirectAttr, HttpServletRequest request, HttpServletResponse response,
			@RequestParam("ulbCode") String ulbCode) {
		logger.info("Entered into checkUlbCodeExists() method..!!");
		int checkAdmUnitsExisted = 0;
		try {
			dto.setUlgCode(ulbCode);
			checkAdmUnitsExisted = service.checkAdmUnitsExistedForULB(dto);
			return checkAdmUnitsExisted;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return checkAdmUnitsExisted;
	}
}