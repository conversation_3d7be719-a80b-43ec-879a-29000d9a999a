package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.AddMandalEntity;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.WithdrawSarpanchDTO;
import cgg.gov.in.apsec.repo.MandalMasterRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.WithdrawSarpanchRepository;

@Controller
public class WithdrawSarpanchController {

	private final Logger logger = LoggerFactory.getLogger(WithdrawSarpanchController.class);

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private MandalMasterRepository mandalMasterRepo;

	@Autowired
	private WithdrawSarpanchRepository withdrawlRepo;

	@GetMapping("/withdrawSarpanch")
	public String withdrawSarpanch(Model model, HttpServletRequest request,
			@ModelAttribute("withdrawSarpanchDto") WithdrawSarpanchDTO withdrawSarpanchDto) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			String mandalId = detailsByUserId.getMandalId();

			withdrawSarpanchDto.setDistrictId(districtId);
			withdrawSarpanchDto.setMandalId(mandalId);

			String eleForPost = withdrawlRepo.getElectionForPost();
			model.addAttribute("eleForPost", eleForPost);

			List<Map<String, Object>> elections = withdrawlRepo.getElections();
			model.addAttribute("electionsList", elections);

			List<Map<String, Object>> districts = null;
			if (districtId != null && !"0".equals(districtId)) {
				districts = withdrawlRepo.getSelectedDistricts(districtId);
			} else {
				districts = withdrawlRepo.getDistricts();
			}

			model.addAttribute("districtsList", districts);
			if (!"0".equals(districtId) && !"0".equals(mandalId)) {
				AddMandalEntity getMandal = mandalMasterRepo.getmandal(districtId, mandalId);
				model.addAttribute("mandalName", getMandal.getMandalName());
				model.addAttribute("mandalId", mandalId);
			}
			model.addAttribute("districtId", districtId);
		} catch (Exception e) {
			logger.error("Error in withdrawSarpanch", e);
		}
		return "withdrawSarpanch";
	}

	@RequestMapping(value = "/withdrawReportGrampanchayat", method = RequestMethod.POST)
	public String withdrawReportGrampanchayat(Model model, HttpServletRequest request,
			@ModelAttribute("withdrawSarpanchDto") WithdrawSarpanchDTO withdrawSarpanchDto) {
		String elecId = withdrawSarpanchDto.getElectionId();
		String distId = withdrawSarpanchDto.getDistrictId();
		String mandalId = withdrawSarpanchDto.getMandalId();
		String gpId = withdrawSarpanchDto.getGpcode().trim();
		if (!("null").equals(gpId) && !"".equals(gpId) && !"0".equals(gpId)) {
			if (gpId.contains(",")) {
				gpId = gpId.replace(",", "");
			}
		} else {
			gpId = "null";
		}

		String nomination = withdrawlRepo.nomination();
		model.addAttribute("nomination_for", nomination);

		List<Map<String, Object>> recNominationsList = withdrawlRepo.getReceivedNominations(elecId, distId, mandalId,
				gpId, nomination);
		model.addAttribute("recNominationsList", recNominationsList);

		model.addAttribute("district_id", distId);
		model.addAttribute("mandal_id", mandalId);
		model.addAttribute("gpcode", gpId);
		model.addAttribute("election_id", elecId);

		String userId = request.getSession().getAttribute("loggedInUser").toString();
		User detailsByUserId = userRepo.getDetailsByUserId(userId);
		String districtId = detailsByUserId.getDistrict_id().toString();
		String mandalId1 = detailsByUserId.getMandalId();

		withdrawSarpanchDto.setDistrictId(districtId);
		withdrawSarpanchDto.setMandalId(mandalId1);

		List<Map<String, Object>> elections = withdrawlRepo.getElections();
		model.addAttribute("electionsList", elections);

		List<Map<String, Object>> districts = null;
		if (districtId != null && !"0".equals(districtId)) {
			districts = withdrawlRepo.getSelectedDistricts(districtId);
		} else
			districts = withdrawlRepo.getDistricts();

		model.addAttribute("districtsList", districts);
		if (!"0".equals(districtId) && !"0".equals(mandalId1)) {
			AddMandalEntity getMandal = mandalMasterRepo.getmandal(districtId, mandalId1);
			model.addAttribute("mandalName", getMandal.getMandalName());
			model.addAttribute("mandalId", mandalId1);
		}
		return "withdrawSarpanch";
	}

	@RequestMapping(value = "/popupWithdrawNominations", method = RequestMethod.POST)
	public String popupWithdrawNominations(RedirectAttributes redirectAttr,
			@ModelAttribute("withdrawSarpanchDto") WithdrawSarpanchDTO withdrawSarpanchDto,
			@RequestParam("slno") Long slno, @RequestParam("remarks") String remarks, HttpServletRequest request) {
		String userId = request.getSession().getAttribute("loggedInUser").toString();
		String elecId = withdrawSarpanchDto.getElectionId();
		String distId = withdrawSarpanchDto.getDistrictId();
		String mandalId = withdrawSarpanchDto.getMandalId();
		String gpId = withdrawSarpanchDto.getGpcode().trim();
		if (!("null").equals(gpId) && !"".equals(gpId) && !"0".equals(gpId)) {
			if (gpId.contains(",")) {
				gpId = gpId.replace(",", "");
			}
		} else {
			gpId = "null";
		}

		int updWithdrawNomination = withdrawlRepo.updWithdrawNomination(remarks, userId, request.getRemoteAddr(), slno);
		if (updWithdrawNomination > 0) {
			redirectAttr.addFlashAttribute("msg", "Withdraw Nomination successfully ");
		} else {
			redirectAttr.addFlashAttribute("msg", "Failed ");
		}

		redirectAttr.addFlashAttribute("district_id", distId);
		redirectAttr.addFlashAttribute("mandal_id", mandalId);
		redirectAttr.addFlashAttribute("gpcode", gpId);
		redirectAttr.addFlashAttribute("election_id", elecId);

		User detailsByUserId = userRepo.getDetailsByUserId(userId);
		String districtId = detailsByUserId.getDistrict_id().toString();
		String mandalId1 = detailsByUserId.getMandalId();

		withdrawSarpanchDto.setDistrictId(districtId);
		withdrawSarpanchDto.setMandalId(mandalId1);

		List<Map<String, Object>> elections = withdrawlRepo.getElections();
		redirectAttr.addFlashAttribute("electionsList", elections);

		List<Map<String, Object>> districts = null;

		if (districtId != null && !"0".equals(districtId)) {
			districts = withdrawlRepo.getSelectedDistricts(districtId);
		} else
			districts = withdrawlRepo.getDistricts();

		redirectAttr.addFlashAttribute("districtsList", districts);
		if (!"0".equals(districtId) && !"0".equals(mandalId1)) {
			AddMandalEntity getMandal = mandalMasterRepo.getmandal(districtId, mandalId1);
			redirectAttr.addFlashAttribute("mandalName", getMandal.getMandalName());
			redirectAttr.addFlashAttribute("mandalId", mandalId1);
		}
		return "redirect:/withdrawSarpanch";
	}

	@RequestMapping(value = "/rollbackPopup", method = RequestMethod.POST)
	public String popupRollbackNominations(RedirectAttributes redirectAttr,
			@ModelAttribute("withdrawSarpanchDto") WithdrawSarpanchDTO withdrawSarpanchDto,
			@RequestParam("rslno") Long rslno, @RequestParam("rremarks") String rremarks, HttpServletRequest request) {

		String userId = request.getSession().getAttribute("loggedInUser").toString();
		String elecId = withdrawSarpanchDto.getElectionId();
		String distId = withdrawSarpanchDto.getDistrictId();
		String mandalId = withdrawSarpanchDto.getMandalId();

		int rollbackNomination = withdrawlRepo.rollbackNomination(rremarks, rslno);
		if (rollbackNomination > 0) {
			redirectAttr.addFlashAttribute("msg", "Withdraw Nomination successfully ");
		} else {
			redirectAttr.addFlashAttribute("msg", "Failed ");
		}
		redirectAttr.addFlashAttribute("district_id", distId);
		redirectAttr.addFlashAttribute("mandal_id", mandalId);
		redirectAttr.addFlashAttribute("election_id", elecId);

		User detailsByUserId = userRepo.getDetailsByUserId(userId);
		String districtId = detailsByUserId.getDistrict_id().toString();
		String mandalId1 = detailsByUserId.getMandalId();

		withdrawSarpanchDto.setDistrictId(districtId);
		withdrawSarpanchDto.setMandalId(mandalId1);

		List<Map<String, Object>> elections = withdrawlRepo.getElections();
		redirectAttr.addFlashAttribute("electionsList", elections);

		List<Map<String, Object>> districts = null;
		if (districtId != null && !"0".equals(districtId)) {
			districts = withdrawlRepo.getSelectedDistricts(districtId);
		} else
			districts = withdrawlRepo.getDistricts();

		redirectAttr.addFlashAttribute("districtsList", districts);
		if (!"0".equals(districtId) && !"0".equals(mandalId1)) {
			AddMandalEntity getMandal = mandalMasterRepo.getmandal(districtId, mandalId1);
			redirectAttr.addFlashAttribute("mandalName", getMandal.getMandalName());
			redirectAttr.addFlashAttribute("mandalId", mandalId1);
		}
		return "redirect:/withdrawSarpanch";
	}
}