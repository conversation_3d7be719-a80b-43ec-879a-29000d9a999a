package cgg.gov.in.apsec.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.DirectVacancyForZPTCEntity;
import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.MasterMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyDLPOZPTCConfirmCustomRepository;
import cgg.gov.in.apsec.repo.VacancyDPOZPTCConfirmRepository;
import cgg.gov.in.apsec.service.AddGPWardService;

@Controller
public class VacancyDPOZPTCConfirmController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private AddGPWardService gpWardSevice;

	@Autowired
	private MasterMandalRepository mastermandalrepo;

	@Autowired
	private VacancyDPOZPTCConfirmRepository vacancyDpoZptcRepo;

	@Autowired
	private VacancyDLPOZPTCConfirmCustomRepository customRepo;

	@Autowired
	private DistrictRepo districtRepo;

	@GetMapping(value = "/vacancyDPOForZPTCConfirm")
	public String vacancyDirectForZPTC(Model model,
			@ModelAttribute("directVacancyForZPTCEntity") DirectVacancyForZPTCEntity directVacancyForZPTCEntity,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();

			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);
				List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
				model.addAttribute("revenueDiv", revenueDiv);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDPOForZPTCConfirm";
	}

	@RequestMapping(value = "/vacancyDPOZptcGet", method = RequestMethod.POST)
	public String vacancyZptcGet(Model model,
			@ModelAttribute("directVacancyForZPTCEntity") DirectVacancyForZPTCEntity directVacancyForZPTCEntity,
			HttpServletRequest request, HttpServletResponse response,
			@RequestParam(name = "districtId", required = false) String districtId,
			@RequestParam(name = "revenueDivisionId", required = false) String revenueDivisionId,
			@RequestParam(name = "assemblyId", required = false) String assemblyId,
			@RequestParam(name = "gpcode", required = false) String gpcode,
			@RequestParam(name = "gpTerritorialConstituency", required = false) String gpTerritorialConstituency) {

		List<Map<String, Object>> vacDPOZptcDtls = vacancyDpoZptcRepo.vacancyDPOZptcGet(districtId, revenueDivisionId,
				assemblyId, gpcode, gpTerritorialConstituency);
		if (districtId != null && !"0".equals(districtId)) {
			District district = districtRepo.findById(districtId).orElse(null);
			Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
			model.addAttribute("districtData", districtData);
			model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
			model.addAttribute("districtId", districtId);
			List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
			model.addAttribute("revenueDiv", revenueDiv);
		}
		model.addAttribute("vacDPOZptcDtls", vacDPOZptcDtls);
		if (vacDPOZptcDtls.size() == 0) {
			model.addAttribute("getMsg", "true");
		}
		return "vacancyDPOForZPTCConfirm";
	}

	@RequestMapping(value = "/vacancyDPOZPTCConfirmSave", method = RequestMethod.POST)
	public String vacancyDPOZPTCConfirmSave(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("assemblyId") String assemblyId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("gpcode") String gpcode,
			@RequestParam("gpTerritorialConstituency") String gpTerritorialConstituency,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDPOZPTCConfirmSave(id, districtId, assemblyId, revenueDivisionId, gpcode,
				gpTerritorialConstituency, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details confirmed successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDPOForZPTCConfirm";
	}

	@RequestMapping(value = "/vacancyDPOZPTCRollbackSave", method = RequestMethod.POST)
	public String vacancyDPOZPTCRollbackSave(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("assemblyId") String assemblyId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("gpcode") String gpcode,
			@RequestParam("gpTerritorialConstituency") String gpTerritorialConstituency,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDPOZPTCRollbackSave(id, districtId, assemblyId, revenueDivisionId, gpcode,
				gpTerritorialConstituency, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details Rolledback successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDPOForZPTCConfirm";
	}

	@GetMapping(value = "/getVacancyDPOZPTCView")
	public String getVacancyDPOZPTCView(Model model,
			@ModelAttribute("directVacancyForZPTCEntity") DirectVacancyForZPTCEntity directVacancyForZPTCEntity) {
		try {
			List<Map<String, Object>> VacancyDPOZPTCCnfRoll = vacancyDpoZptcRepo.vacancyDpoZptcView();
			model.addAttribute("VacancyDPOZPTCCnfRoll", VacancyDPOZPTCCnfRoll);

			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDPOForZPTCConfirmRollbackList";
	}
}