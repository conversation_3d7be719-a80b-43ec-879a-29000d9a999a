package cgg.gov.in.apsec.controller;

import java.security.Principal;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.pojo.ZPTCNotifiedForElectionsDTO;
import cgg.gov.in.apsec.service.ZPTCNotifiedForElectionsService;

@Controller
public class ZPTCNotifiedForElectionsController {

	@Autowired
	private ZPTCNotifiedForElectionsService zptcNotifiedForElectionsService;

	@RequestMapping(value = "/zptcNotifiedForElections")
	public String zptcNotifiedForElections(Principal principal, Model model,
			@ModelAttribute("zptcNotifiedForElectionsDTO") ZPTCNotifiedForElectionsDTO zptcNotifiedForElectionsDTO) {
		try {
			List<Map<String, Object>> electionTypeList = zptcNotifiedForElectionsService.getElectionTypeList();
			model.addAttribute("electionTypeList", electionTypeList);
			model.addAttribute("zptcNotifiedForElectionsDTO", zptcNotifiedForElectionsDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "zptcNotifiedForElections";
	}

	@RequestMapping(value = "/zptcNotifiedForElectionsReport")
	public String zptcNotifiedForElectionsReport(Principal principal, Model model,
			@ModelAttribute("zptcNotifiedForElectionsDTO") ZPTCNotifiedForElectionsDTO zptcNotifiedForElectionsDTO,
			RedirectAttributes redirectAttr, HttpServletRequest request, HttpServletResponse response) {
		try {
			model.addAttribute("zptcNotifiedForElectionsDTO", zptcNotifiedForElectionsDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "zptcNotifiedForElectionsReport";
	}
}