package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;

import cgg.gov.in.apsec.modal.AddMandalEntity;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.GPwiseVoterListRuralDTO;
import cgg.gov.in.apsec.repo.GpwiseRepoforMysql;
import cgg.gov.in.apsec.repo.MandalMasterRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.GPwiseVoterListRuralService;

@Controller
public class WorkingERollsController {

	private final Logger logger = LoggerFactory.getLogger(GPwiseVoterListRuralController.class);

	@Autowired
	private MandalMasterRepository mandalMasterRepo;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private GPwiseVoterListRuralService gpVLRuralService;

	@Autowired
	private GpwiseRepoforMysql gpmysqlRepo;

	@GetMapping("/getWorkingERolls")
	public String getWorkingERolls(Model model, @ModelAttribute("gpVLRuralDto") GPwiseVoterListRuralDTO gpVLRuralDto,
			HttpServletRequest request) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id().toString();
			String mandalId = detailsByUserId.getMandalId();

			gpVLRuralDto.setDistrictId(districtId);
			gpVLRuralDto.setMandalId(mandalId);

			List<Map<String, Object>> elections = gpVLRuralService.getElections();
			model.addAttribute("electionsList", elections);

			List<Map<String, Object>> districts = null;
			if (districtId != null && !"0".equals(districtId)) {
				districts = gpVLRuralService.getSelectedDistricts(districtId);
			} else {
				districts = gpVLRuralService.getDistricts();
			}
			model.addAttribute("districtsList", districts);
			if (!"0".equals(districtId) && !"0".equals(mandalId)) {
				AddMandalEntity getMandal = mandalMasterRepo.getmandal(districtId, mandalId);
				model.addAttribute("mandalName", getMandal.getMandalName());
				model.addAttribute("mandalId", mandalId);
			}
			model.addAttribute("districtId", districtId);
			model.addAttribute("initialLoad", "initialLoad");
		} catch (Exception e) {
			logger.error("Error in getGPWardPSFormByLogin", e);
		}
		return "workingERolls";
	}

	@PostMapping("getDataForERolls")
	public String getDataForERolls(Model model, @ModelAttribute("gpVLRuralDto") GPwiseVoterListRuralDTO gpVLRuralDto) {
		List<Map<String, Object>> gpwiseDataList = gpmysqlRepo.getGPWiseData(gpVLRuralDto);
		model.addAttribute("gpwiseDataList", gpwiseDataList);

		model.addAttribute("electionsList", gpVLRuralService.getElections());
		model.addAttribute("districtsList", gpVLRuralService.getSelectedDistricts(gpVLRuralDto.getDistrictId()));
		if (!"0".equals(gpVLRuralDto.getDistrictId()) && !"0".equals(gpVLRuralDto.getMandalId())) {
			AddMandalEntity getMandal = mandalMasterRepo.getmandal(gpVLRuralDto.getDistrictId(),
					gpVLRuralDto.getMandalId());
			model.addAttribute("mandalName", getMandal.getMandalName());
			model.addAttribute("mandalId", gpVLRuralDto.getMandalId());
		}
		model.addAttribute("gpVLRuralDto", gpVLRuralDto);
		return "workingERolls";
	}
}