package cgg.gov.in.apsec.asdvoter.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.AddMandalEntity;
import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.ListofAsdVotersPSWiseDTO;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.MandalMasterRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.ElectionProcessUrbanLocalBodiesService;
import cgg.gov.in.apsec.service.GPwiseVoterListRuralService;
import cgg.gov.in.apsec.service.ListofAsdVotersPSWiseService;
import cgg.gov.in.apsec.service.MptcZptcERprintService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class ASDVotersEntryMPTCWiseController {

	private final Logger logger = LoggerFactory.getLogger(ASDVotersEntryMPTCWiseController.class);

	@Autowired
	private MandalMasterRepository mandalMasterRepo;

	@Autowired
	private DistrictRepo districtRepo;

	@Autowired
	private ListofAsdVotersPSWiseService listofAsdVotersPSWiseService;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private MptcZptcERprintService mptcZptcERprintService;

	@Autowired
	private GPwiseVoterListRuralService gpVLRuralService;

	@RequestMapping(value = "/mptcWiseAsdVotersEntry", method = RequestMethod.GET)
	public String mptcWiseAdsVotersEntry(
			@ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
			HttpServletRequest request, HttpServletResponse response, Model model) {
		logger.info("ENTERING INTO mptcWiseAdsVotersEntry() in GPWardPSController ");
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepository.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id().toString();
			String mandalId = detailsByUserId.getMandalId();
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);

			District district = districtRepo.findById(districtId).orElse(null);
			AddMandalEntity getMandal = mandalMasterRepo.getmandal(districtId, mandalId);

			model.addAttribute("mandalName", getMandal.getMandalName());
			model.addAttribute("districtName", district.getDistrictName());

			String district_id = detailsByUserId.getDistrict_id();
			String revDivisionId = detailsByUserId.getRevenueDivisionId();

			String districtName = listofAsdVotersPSWiseService.getDistrictName(district_id);
			model.addAttribute("districtName", districtName);

			String mandalName = listofAsdVotersPSWiseService.getMandalName(district_id, revDivisionId, mandalId);
			model.addAttribute("mandalName", mandalName);

			/*
			 * List<Map<String, Object>> gpNamesList =
			 * listofAsdVotersPSWiseService.getgpNamesList(district_id, revDivisionId,
			 * mandalId); model.addAttribute("gpNamesList", gpNamesList);
			 */

			if (mandalId == null || mandalId.equals("0")) {
				List<Map<String, Object>> mandaList = userRepository.getMPPList(districtId, revDivisionId);
				model.addAttribute("mppList", mandaList);
			} else {
				String userMppName = userRepository.getMandalNameInt(Integer.parseInt(districtId),
						Integer.parseInt(revDivisionId), Integer.parseInt(mandalId));
				model.addAttribute("userMppId", mandalId);
				model.addAttribute("userMppName", userMppName);

				List<Map<String, Object>> mptcNames = mptcZptcERprintService.getMptcNamesByIds(districtId,
						revDivisionId, mandalId + "_" + mandalName);
				model.addAttribute("mptcNames", mptcNames);
			}

			logger.info("districtId   " + districtId + " and  mandalId  " + mandalId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "mptcWiseAdVotersForm";
	}

	@RequestMapping(value = "/getGpByDistMdlMPTC1", method = RequestMethod.GET)
	public @ResponseBody String getGpByDistMdlMPTC1(HttpServletRequest request, @RequestParam("mptcId") String mptcId) {
		StringBuilder mainData = new StringBuilder();
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepository.getDetailsByUserId(userId);
			String district_id = detailsByUserId.getDistrict_id();
			String revDivisionId = detailsByUserId.getRevenueDivisionId();
			String mandalId = detailsByUserId.getMandalId();

			List<Map<String, Object>> gpList = gpVLRuralService.getGpByDistMdlMPTC(district_id, mandalId, mptcId);
			for (Map<String, Object> map : gpList) {
				mainData.append("<option value='" + map.get("gpcode") + "'>" + map.get("gpname") + "</option>");
			}
		} catch (Exception e) {
			logger.error("Error at fetching the Grampanchayats.." + e.getMessage());
		}
		return mainData.toString();
	}

	@RequestMapping(value = "getPSNoListMPTCZPTC", method = RequestMethod.GET)
	public @ResponseBody String getPSNoListMPTCZPTC(HttpServletRequest request, @RequestParam("mptc") String mptc,
			@RequestParam("gpcode") String gpcode) {
		StringBuilder psNoData = new StringBuilder();
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepository.getDetailsByUserId(userId);
			String district_id = detailsByUserId.getDistrict_id();
			String revDivisionId = detailsByUserId.getRevenueDivisionId();
			String mandalId = detailsByUserId.getMandalId();
			System.out.println("district_id is " + district_id);
			System.out.println("revDivisionId is " + revDivisionId);
			System.out.println("mandalId is " + mandalId);
			System.out.println("mptc is " + mptc);

			String extractedMptc = mptc.split("_")[0];

			System.out.println("Extracted mptc is " + extractedMptc);

			List<Map<String, Object>> psNoDataList = listofAsdVotersPSWiseService.getPSNumDataMPTCZPTC(district_id,
					revDivisionId, mandalId, extractedMptc);
			for (Map<String, Object> map : psNoDataList) {
				psNoData.append(
						"<option value='" + map.get("pollingstation_id") + "'>" + map.get("ps_no") + "</option>");
			}
		} catch (Exception e) {
			logger.error("Error at fetching the Gp Wards.." + e.getMessage());
		}
		return psNoData.toString();
	}

	@PostMapping("/insertMPTCZPTCASDVotersData")
	public String insertMPTCZPTCASDVotersData(
			@ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
			RedirectAttributes redirectAttr, HttpServletRequest request) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepository.getDetailsByUserId(userId);

			String district_id = detailsByUserId.getDistrict_id();
			String revDivisionId = detailsByUserId.getRevenueDivisionId();
			String mandalId = detailsByUserId.getMandalId();

			int result = listofAsdVotersPSWiseService.insertMPTCZPTCASDVotersData(userId, district_id, revDivisionId,
					mandalId, listofAsdVotersPSWiseDto);
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess",
						" List of ASD Voters for PS Wise Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed List of ASD Voters for PS Wise data ...!!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/mptcWiseAsdVotersEntry";
	}

	@GetMapping("/getMPTCZPTCOwnerNameandDoorNoCheckExist")
	@ResponseBody
	public Boolean getMPTCZPTCOwnerNameandDoorNoCheckExist(@RequestParam("gpCode") String gpCode,
			@RequestParam("voteringpEr") String voteringpEr, @RequestParam("psNo") String psNo) {
		int ownerNameandDoorNoCheckExist = 0;
		try {
			ownerNameandDoorNoCheckExist = listofAsdVotersPSWiseService.getMPTCZPTCOwnerNameandDoorNoCheckExist(gpCode,
					voteringpEr, psNo);
			if (ownerNameandDoorNoCheckExist > 0) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	@GetMapping(value = "/fetchMPTCZPTCListofASDVotersPSWise")
	public String fetchMPTCZPTCListofASDVotersPSWise(HttpServletRequest request,
			@ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
			Model model) {
		String userId = request.getSession().getAttribute("loggedInUser").toString();
		User detailsByUserId = userRepository.getDetailsByUserId(userId);
		String token = TokenUtil.generateToken(request.getSession());
		model.addAttribute("formToken", token);

		String district_id = detailsByUserId.getDistrict_id();
		String revDivisionId = detailsByUserId.getRevenueDivisionId();
		String mandalId = detailsByUserId.getMandalId();
		String distName = userRepository.getDistrictName(district_id);
		String mandalName = userRepository.getMandalName(district_id, mandalId);
		String devisionName = userRepository.getRevenueDivisionName(revDivisionId, district_id);
		model.addAttribute("distName", distName);
		model.addAttribute("mandalName", mandalName);
		model.addAttribute("devisionName", devisionName);
		model.addAttribute("mptczptc", "mptczptc");
		/*
		 * List<Map<String, Object>> gpNamesList =
		 * listofAsdVotersPSWiseService.getgpNamesList(district_id, revDivisionId,
		 * mandalId); model.addAttribute("gpNamesList", gpNamesList);
		 */

		if (mandalId == null || mandalId.equals("0")) {
			List<Map<String, Object>> mandaList = userRepository.getMPPList(district_id, revDivisionId);
			model.addAttribute("mppList", mandaList);
		} else {
			String userMppName = userRepository.getMandalNameInt(Integer.parseInt(district_id),
					Integer.parseInt(revDivisionId), Integer.parseInt(mandalId));
			model.addAttribute("userMppId", mandalId);
			model.addAttribute("userMppName", userMppName);

			List<Map<String, Object>> mptcNames = mptcZptcERprintService.getMptcNamesByIds(district_id, revDivisionId,
					mandalId + "_" + mandalName);
			model.addAttribute("mptcNames", mptcNames);
		}

//	List<Map<String, Object>> mandalWiseASDVoterDetails = listofAsdVotersPSWiseService.getListofMPTCZPTCASDVoterPSWise(district_id, mandalId);
//	model.addAttribute("listofASDVoterPSWiseList", mandalWiseASDVoterDetails);
//	model.addAttribute("abstractReport",
//			listofAsdVotersPSWiseRepository.getASDVotersCountBycategoaryMPTCZPTC(district_id, mandalId));
		return "fetchListofASDVotersPSWise";
	}

	@PostMapping(value = "/getMPTCWiseASDVoterReport")
	public String getMPTCWiseASDVoterReport(HttpServletRequest request,
			@ModelAttribute("listofAsdVotersPSWiseDto") ListofAsdVotersPSWiseDTO listofAsdVotersPSWiseDto,
			Model model) {
		String userId = request.getSession().getAttribute("loggedInUser").toString();
		User detailsByUserId = userRepository.getDetailsByUserId(userId);
		String district_id = detailsByUserId.getDistrict_id();
		String revDivisionId = detailsByUserId.getRevenueDivisionId();
		String mandalId = detailsByUserId.getMandalId();
		String distName = userRepository.getDistrictName(district_id);
		String mandalName = userRepository.getMandalName(district_id, mandalId);
		String devisionName = userRepository.getRevenueDivisionName(revDivisionId, district_id);
		String gpCode = listofAsdVotersPSWiseDto.getGp_name();
		String mptc = listofAsdVotersPSWiseDto.getMptcNameId();
		/*
		 * List<Map<String, Object>> psNumData =
		 * listofAsdVotersPSWiseService.getPSNumData(district_id, revDivisionId,
		 * mandalId, gpCode);
		 */
//		List<Map<String, Object>> psNumData = listofAsdVotersPSWiseService.getPSNumData(district_id, revDivisionId,
//				mandalId, mptc);
//		model.addAttribute("psNoData", psNumData);
		model.addAttribute("distName", distName);
		model.addAttribute("mandalName", mandalName);
		model.addAttribute("devisionName", devisionName);
		model.addAttribute("mptczptc", "mptczptc");
//		List<Map<String, Object>> gpNamesList = listofAsdVotersPSWiseService.getgpNamesList(district_id, revDivisionId,
//				mandalId);
		List<Map<String, Object>> gpNamesList =  gpVLRuralService.getGpByDistMdlMPTC(district_id, mandalId, mptc);
		model.addAttribute("gpList", gpNamesList);
		
		
		String extractedMptc = mptc.split("_")[0];

		List<Map<String, Object>> psNoDataList = listofAsdVotersPSWiseService.getPSNumDataMPTCZPTC(district_id,
				revDivisionId, mandalId, extractedMptc);
		model.addAttribute("psNoData", psNoDataList);
		
		List<Map<String, Object>> mandalWiseASDVoterDetails = listofAsdVotersPSWiseService
				.getListofMPTCZPTCASDVoterPSWise(district_id, mandalId, listofAsdVotersPSWiseDto.getGp_name(),
						listofAsdVotersPSWiseDto.getPollingstation_no());
		model.addAttribute("listofASDVoterPSWiseList", mandalWiseASDVoterDetails);
		// model.addAttribute("abstractReport",
		// listofAsdVotersPSWiseService.getASDVotersCountByCategoryMPTCZPTC(district_id,
		// mandalId,
		// listofAsdVotersPSWiseDto.getGp_name(),
		// listofAsdVotersPSWiseDto.getPollingstation_no()));
		List<Map<String, Object>> mptcNames = mptcZptcERprintService.getMptcNamesByIds(district_id, revDivisionId,
				mandalId + "_" + mandalName);
		model.addAttribute("mptcNames", mptcNames);
		
		model.addAttribute("abstractReport",
				listofAsdVotersPSWiseService.getASDVotersCountByCategoryMPTCZPTCWithoutGpCode(district_id, mandalId,
						listofAsdVotersPSWiseDto.getPollingstation_no()));
		return "fetchListofASDVotersPSWise";
	}

	@RequestMapping(value = "getFromToSlNoMPTCZPTC", method = RequestMethod.GET)
	public @ResponseBody String getFromToSlNoMPTCZPTC(HttpServletRequest request, @RequestParam("gpcode") String gpcode,
			@RequestParam("psNo") String psNo) {
		StringBuilder resultFromToSlNo = new StringBuilder();
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepository.getDetailsByUserId(userId);
			String district_id = detailsByUserId.getDistrict_id();
			String revDivisionId = detailsByUserId.getRevenueDivisionId();
			String mandalId = detailsByUserId.getMandalId();

			List<Map<String, Object>> psNoDataList = listofAsdVotersPSWiseService.getFromToSlNoMPTCZPTC(district_id,
					revDivisionId, mandalId, gpcode, psNo);
			for (Map<String, Object> map : psNoDataList) {
				resultFromToSlNo.append(map.get("from_serialno")).append(",").append(map.get("to_serialno"));
			}
		} catch (Exception e) {
			logger.error("Error at fetching the Gp--------------- Wards.." + e.getMessage());
		}
		return resultFromToSlNo.toString();
	}

	@RequestMapping(value = "getFromToSlNoMPTCZPTCNew", method = RequestMethod.GET)
	public @ResponseBody String getFromToSlNoMPTCZPTCNew(HttpServletRequest request,
			@RequestParam("gpcode") String gpcode, @RequestParam("psNo") String psNo) {
		StringBuilder resultFromToSlNo = new StringBuilder();
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepository.getDetailsByUserId(userId);
			String district_id = detailsByUserId.getDistrict_id();
			String revDivisionId = detailsByUserId.getRevenueDivisionId();
			String mandalId = detailsByUserId.getMandalId();

			List<Map<String, Object>> psNoDataList = listofAsdVotersPSWiseService.getFromToSlNoMPTCZPTCNew(district_id,
					revDivisionId, mandalId, gpcode, psNo);
			for (Map<String, Object> map : psNoDataList) {
				Object from = map.get("from_serialno");
				Object to = map.get("to_serialno");

				resultFromToSlNo.append(from != null ? from.toString() : "").append(",")
						.append(to != null ? to.toString() : "");
			}
//			for (Map<String, Object> map : psNoDataList) {
//	            resultFromToSlNo.append(map.get("from_serialno")).append(",")
//	                            .append(map.get("to_serialno"));
//	        }
		} catch (Exception e) {
			logger.error("Error at fetching the Gp--------------- Wards.." + e.getMessage());
		}
		return resultFromToSlNo.toString();
	}
}