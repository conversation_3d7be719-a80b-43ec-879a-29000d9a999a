package cgg.gov.in.apsec.controller;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.CommonPortalDTO;
import cgg.gov.in.apsec.pojo.DoorNumberUpdationDTO;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationMptcDTO;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationUlbDTO;
import cgg.gov.in.apsec.repo.DirectElectionSchedulePDFCustomRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationMptcRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationUlbRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationZptcRepository;
import cgg.gov.in.apsec.service.ULGWiseERPrintService;
import cgg.gov.in.apsec.service.WithdrawalCandidatureNominationMptcService;
import cgg.gov.in.apsec.service.WithdrawalCandidatureNominationUlbService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class WithdrawalCandidatureNominationUlbController {
	
	@Autowired
	private UserRepository userRepo;
	
	@Autowired
	private WithdrawalCandidatureNominationUlbService withdrawalCandidatureNominationUlbService;

	@Autowired
	private WithdrawalCandidatureNominationUlbRepository withdrawalCandidatureNominationUlbRepository;
	
	@Autowired
	private ULGWiseERPrintService ulgWiseERPrintService;
	
	@Autowired
	private DirectElectionSchedulePDFCustomRepository directElectionScheduleCustomRepo;

	
	@RequestMapping(value = "/withdrawalCandidatureNominationForUlb")
	public String withdrawalCandidatureNominationForUlb(Model model, HttpServletRequest request,
			@ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto) {
		 
			List<Map<String, Object>> withDrawalNominationList = null;
		    List<Map<String, Object>> withDrawalStatusList = null;
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);

			String districtId = detailsByUserId.getDistrict_id().toString();
			String ulbCode = detailsByUserId.getUlgCode();
			String wardNo = detailsByUserId.getWardId();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			
			List<Map<String, Object>> acList = userRepo.assemblyListULBByULG(districtId,
					ulbCode,Integer.parseInt(wardNo));

			model.addAttribute("districtId", districtId);
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
			model.addAttribute("assemblyList", acList);
			model.addAttribute("wardMem", "wardMem");
			model.addAttribute("ulbCode", ulbCode);

			model.addAttribute("ulbType",ulbDetails.get("ulb_type"));

			model.addAttribute("wardNo",wardNo);
			model.addAttribute("ulgName",ulbDetails.get("ulb_name"));
			
			withDrawalNominationList = withdrawalCandidatureNominationUlbService
 					.getwithdrawalNominationDataListUlb(districtId, ulbCode, ulbType, wardNo);
 			model.addAttribute("withDrawalNominationList", withDrawalNominationList);

 			withDrawalStatusList = withdrawalCandidatureNominationUlbService.getWithDrawaStatusListUlb(districtId, ulbCode,
 					ulbType,wardNo);
 			model.addAttribute("withDrawalStatusList", withDrawalStatusList);
            model.addAttribute("slectedWardNo", wardNo);

		
	} catch (Exception e) {
		e.printStackTrace();
	}
	return "withdrawalCandidatureNominationForUlb";
}
	
	@PostMapping("/getDetailsForUlbWithDraw")
	public String getDetailsForUlbWithDraw(RedirectAttributes redirectAttr,HttpServletRequest request,
	        @ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto) 
			throws Exception{
	    try {
	    	String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String ulbCode = detailsByUserId.getUlgCode(); 
			String districtId = detailsByUserId.getDistrict_id().toString();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			if(!districtId.equals(withdrawalCandidatureNominationUlbDto.getDistrictId()) || !ulbCode.equals(withdrawalCandidatureNominationUlbDto.getUlbCode()) || !ulbType.equals(withdrawalCandidatureNominationUlbDto.getUlbType())) {
				throw new AccessDeniedException("Unauthorized access attempt detected.");
			}

			redirectAttr.addFlashAttribute("ulbType",ulbDetails.get("ulb_type"));
	        redirectAttr.addFlashAttribute("wardNo", withdrawalCandidatureNominationUlbDto.getWardNo());
	    } catch (Exception e) {
	        e.printStackTrace();
			if (e instanceof AccessDeniedException) {
				throw e;
			}
	    }
	    return "redirect:/withdrawalCandidatureNominationForUlb";
	}
	@PostMapping("ulbwithdrawnStatus")
	@ResponseBody
	public ResponseEntity<Map<String, Object>> withdrawnStatus(
	        @ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto,
	        @RequestParam("slnoList") String slnoList,
	        @RequestParam("district_Id") String districtId,
	        @RequestParam("ulb_Code") String ulgCode,
	        @RequestParam("ulb_Type") String ulgType,
	        @RequestParam("ward_No") String wardNo,
	        @RequestParam("processComplete") int processComplete,HttpServletRequest request) {

	    Map<String, Object> response = new HashMap<>();
	    try {
	    	String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId1 = detailsByUserId.getDistrict_id().toString();
			String ulbCode1 = detailsByUserId.getUlgCode(); 
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId1),Integer.parseInt(ulbCode1));
			String ulbType1 = (String) ulbDetails.get("ulb_type");
	        ObjectMapper objectMapper = new ObjectMapper();
	        List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
	                new TypeReference<List<Map<String, String>>>() {});
	        

	        // Update withdrawal nominations
	        int result = withdrawalCandidatureNominationUlbService.updateUlbWithdrawalNomination(slnoListMap);
	        if (result > 0) {
	            response.put("success", true);
	            response.put("message", "Withdrawal process completed successfully.");
	        } else {
	            response.put("success", false);
	            response.put("message", "Failed to complete the withdrawal process.");
	            return ResponseEntity.ok(response);
	        }

	        // Check if data is already confirmed
	        int existingCount = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbConfirmCountAjax(
	                districtId, ulbCode1, ulbType1, wardNo);
	        if (existingCount > 0) {
	            response.put("success", false);
	            response.put("message", "Data already confirmed!");
	            return ResponseEntity.ok(response);
	        }

	        // Process completion confirmation
	        int roCount = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbConfirmByROAjax(
	                districtId, ulbCode1, ulbType1, wardNo, processComplete);
	        if (roCount > 0) {
	            response.put("success", true);
	            response.put("message", "Process completed and data updated.");
	        } else {
	            response.put("success", false);
	            response.put("message", "No data updated during process completion.");
	        }
	    } catch (Exception e) {
	        e.printStackTrace();
	        response.put("success", false);
	        response.put("message", "An error occurred during processing: " + e.getMessage());
	    }

	    return ResponseEntity.ok(response);
	}
	
	@RequestMapping(value = "/receiptUlbAcknowledged")
	public String receiptUlbAcknowledged(Model model, @RequestParam("slno") String slno) {
		try {
			model.addAttribute("slno", slno);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "receiptAcknowledgedforUlb";
	}

	@RequestMapping(value = "/reportAKWUlbData")
	 public ResponseEntity<String> reportACKFormUlbData(Model model, 
			 @ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto,            
       RedirectAttributes redirectAttr,HttpServletRequest request,
         @RequestParam("akwcandidateSigned") String akwcandidateSigned,
         @RequestParam("akwbywhomeulbformSubmite") String akwbywhomeulbformSubmite,
         @RequestParam("dateFieldOne") String dateFieldOne,
         @RequestParam("timeFieldTwo") String timeFieldTwo,
         @RequestParam("slno") Integer slno) {	     
	     try {
	    	 String userId = request.getSession().getAttribute("loggedInUser").toString();
	    	 WithdrawalCandidatureNominationUlbDTO dto=new WithdrawalCandidatureNominationUlbDTO();
	    	 dto.setAkwulbcandidateSigned(akwcandidateSigned);
	    	 dto.setAkwbywhomeulbformSubmite(akwbywhomeulbformSubmite);
	    	
	    	 dto.setDateFieldOne(dateFieldOne);
	    	 dto.setTimeFieldTwo(timeFieldTwo);
			int result = withdrawalCandidatureNominationUlbService
					.insertreportAKWFormInfo(withdrawalCandidatureNominationUlbDto, userId, slno.toString(),akwcandidateSigned,akwbywhomeulbformSubmite);
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess", " ReportAcknowlwdged Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed ReportAcknowlwdged Data ...!!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	     return ResponseEntity.ok("Success");
	}
	
	@RequestMapping(value = "/getWithdrawlUlbConfirmCountAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlUlbConfirmCountAjax(@RequestParam(value = "districtId") String decodedistrictId, @RequestParam(value = "ulbCode") String decodeulbCode,
			@RequestParam(value = "ulbType") String decodeulbType,  @RequestParam(value = "wardNo") String decodewardNo,HttpServletRequest request) {
		int count = 0;
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			
			String districtId = new String(Base64.getDecoder().decode(decodedistrictId), StandardCharsets.UTF_8);
	        String ulbCode = new String(Base64.getDecoder().decode(decodeulbCode), StandardCharsets.UTF_8);
	        String ulbType = new String(Base64.getDecoder().decode(decodeulbType), StandardCharsets.UTF_8);
	        String wardNo = new String(Base64.getDecoder().decode(decodewardNo), StandardCharsets.UTF_8);

			String districtId1 = detailsByUserId.getDistrict_id().toString();
			String ulbCode1 = detailsByUserId.getUlgCode(); 
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId1),Integer.parseInt(ulbCode1));
			String ulbType1 = (String) ulbDetails.get("ulb_type");
			count = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbConfirmCountAjax1(districtId, ulbCode1, ulbType1,wardNo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	@RequestMapping(value = "/getWithdrawlUlbConfirmByROAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlUlbConfirmByROAjax(@RequestParam(value = "districtId") String decodedistrictId, @RequestParam(value = "ulbCode") String decodeulbCode,
			@RequestParam(value = "ulbType") String decodeulbType,  @RequestParam(value = "wardNo") String decodewardNo,
			@RequestParam(value = "processComplete") int processComplete,HttpServletRequest request) {
		int count = 0;
		try {
			
			String districtId = new String(Base64.getDecoder().decode(decodedistrictId), StandardCharsets.UTF_8);
	        String ulbCode = new String(Base64.getDecoder().decode(decodeulbCode), StandardCharsets.UTF_8);
	        String ulbType = new String(Base64.getDecoder().decode(decodeulbType), StandardCharsets.UTF_8);
	        String wardNo = new String(Base64.getDecoder().decode(decodewardNo), StandardCharsets.UTF_8);
	        
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId1 = detailsByUserId.getDistrict_id().toString();
			String ulbCode1 = detailsByUserId.getUlgCode(); 
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId1),Integer.parseInt(ulbCode1));
			String ulbType1 = (String) ulbDetails.get("ulb_type");
				if(processComplete == 1) {
					count = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbConfirmByROAjax1(districtId, ulbCode1, ulbType1,wardNo, processComplete);
				}else if(processComplete == 2){
					int processCount = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbProcessCount(districtId, ulbCode1, ulbType1,wardNo);
					if(processCount > 0) {
						int updateCount = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbConfirmByROAjax1(districtId, ulbCode1, ulbType1,wardNo, processComplete);
						if(updateCount > 0) {
							count = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbDraftCount(districtId, ulbCode1, ulbType1,wardNo);
						}	
					}
				}else if(processComplete == 3){
					int draftCount =  withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbDraftCount(districtId, ulbCode1, ulbType1, wardNo);
					if(draftCount > 0) {
						count = withdrawalCandidatureNominationUlbRepository.getWithdrawlUlbConfirmByROAjax1(districtId, ulbCode1, ulbType1, wardNo, processComplete);
					}
				}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	
	@RequestMapping(value = "/fetchFormXUlbData", method = RequestMethod.GET)
	public String fetchFormXUlbData(HttpServletRequest request, @RequestParam(value = "districtId") String districtId, @RequestParam(value = "ulbCode") String ulbCode,
			@RequestParam(value = "ulbType") String ulbType,  @RequestParam(value = "wardNo") String wardNo, Model model) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId1 = detailsByUserId.getDistrict_id().toString();
			String ulbCode1 = detailsByUserId.getUlgCode();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId1),Integer.parseInt(ulbCode1));
			String ulbType1 = (String) ulbDetails.get("ulb_type");
			 model.addAttribute("ulbType1", ulbType1);
			
			String UlgTeluguName = withdrawalCandidatureNominationUlbRepository.getUlgTeluguName(districtId, ulbType1, ulbCode1);
			model.addAttribute("UlgTeluguName", UlgTeluguName);

			model.addAttribute("wardNo", wardNo);
			
			List<Map<String, Object>> formeightData =
					withdrawalCandidatureNominationUlbRepository.getFormXUlbdatanew(districtId, ulbCode1, ulbType1,wardNo);

			String roUploadsignature = withdrawalCandidatureNominationUlbRepository.getUlbROUploadsignature(userId);
			model.addAttribute("roUploadsignature", roUploadsignature);
			

			model.addAttribute("formeightDataList", formeightData);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchFormXUlbforTelugu";
	}
	
	@GetMapping("/getFormXUlbMcNp")
	public String getFormXUlbMcNp(
			@ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto,
			HttpServletRequest request, Model model) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String districtId = detailsByUserId.getDistrict_id().toString();
			String ulbCode = detailsByUserId.getUlgCode();
			String wardNo = detailsByUserId.getWardId();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			
			List<Map<String, Object>> acList = userRepo.assemblyListULBByULG(districtId,
					ulbCode,Integer.parseInt(wardNo));

			model.addAttribute("districtId", districtId);
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
			model.addAttribute("assemblyList", acList);
			model.addAttribute("wardMem", "wardMem");
			model.addAttribute("ulbCode", ulbCode);

			model.addAttribute("ulbType",ulbDetails.get("ulb_type"));

			model.addAttribute("wardNo",wardNo);
			model.addAttribute("ulgName",ulbDetails.get("ulb_name"));

			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "formXForUlbMcNp";
	}
	
	@RequestMapping(value = "/fetchUlbFormAKWInfo")
	public String fetchUlbFormAKWInfo(HttpServletRequest request ,Model model, @RequestParam("slno") String slno) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String districtId = detailsByUserId.getDistrict_id().toString();
			String ulbCode = detailsByUserId.getUlgCode();
			String wardNo = detailsByUserId.getWardId();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			
			String UlgEnglishName = withdrawalCandidatureNominationUlbRepository.getUlgEnglishName(districtId, ulbType, ulbCode);
			model.addAttribute("UlgEnglishName", UlgEnglishName);

			model.addAttribute("wardNo", wardNo);
			model.addAttribute("ulbType", ulbType);

			String  akwulbCandidateName =  withdrawalCandidatureNominationUlbService.akwUlbName(slno);
			model.addAttribute("akwulbCandidateName", akwulbCandidateName);

			String  akwcandidateDate =  withdrawalCandidatureNominationUlbService.akwDate(slno);
			model.addAttribute("akwcandidateDate", akwcandidateDate);

			String  akwcandidateTime =  withdrawalCandidatureNominationUlbService.akwTime(slno);
			model.addAttribute("akwcandidateTime", akwcandidateTime);

			String akwbywhomeformSubmit = withdrawalCandidatureNominationUlbService.akwbywhomeformSubmit(slno);
			model.addAttribute("akwbywhomeformSubmit", akwbywhomeformSubmit);
			String whomSubmitted="";
			if(akwbywhomeformSubmit.equals("1")) {
				whomSubmitted=	akwulbCandidateName;
			}
			if(akwbywhomeformSubmit.equals("2")) {
				whomSubmitted=withdrawalCandidatureNominationUlbRepository.whomSubmitted_proposer_ulb(slno);
			}
			if(akwbywhomeformSubmit.equals("3")) {
				whomSubmitted=withdrawalCandidatureNominationUlbRepository.whomSubmitted_election_agent_ward(detailsByUserId.getDistrict_id(),ulbType, ulbCode,wardNo);
			}
			model.addAttribute("whomSubmitted",whomSubmitted);
			
			String roUploadsignature = withdrawalCandidatureNominationUlbRepository.getUlbROUploadsignature(userId);
			model.addAttribute("roUploadsignature", roUploadsignature);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return "formIXForUlbDataMc";
	}
	@GetMapping("/getFormIUlbMcNp")
	public String getFormIUlbMcNp(
			@ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto,
			HttpServletRequest request, Model model) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String districtId = detailsByUserId.getDistrict_id().toString();
			String ulbCode = detailsByUserId.getUlgCode();
			String wardNo = detailsByUserId.getWardId();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			
			List<Map<String, Object>> acList = userRepo.assemblyListULBByULG(districtId,
					ulbCode,Integer.parseInt(wardNo));
			if(ulbType.split("_")[0].equals("1")) {
			List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbNp(userId);
			model.addAttribute("electionsList", elections);
			}
			else if(ulbType.split("_")[0].equals("2")) {
				List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbMpl(userId);
				model.addAttribute("electionsList", elections);
				}
			else if(ulbType.split("_")[0].equals("3")) {
				List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbMc(userId);
				model.addAttribute("electionsList", elections);
				}
			else if(ulbType.split("_")[0].equals("4")) {
				List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbVmc(userId);
				model.addAttribute("electionsList", elections);
				}
			else  {
				List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbGvmc(userId);
				model.addAttribute("electionsList", elections);
				}
			
			model.addAttribute("districtId", districtId);
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
			model.addAttribute("assemblyList", acList);
			model.addAttribute("wardMem", "wardMem");
			model.addAttribute("ulbCode", ulbCode);

			model.addAttribute("ulbType",ulbDetails.get("ulb_type"));

			model.addAttribute("wardNo",wardNo);
			model.addAttribute("ulgName",ulbDetails.get("ulb_name"));

			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "formIForUlbMcNp";
	}
	@GetMapping("/electionNoticeFormIforULB")
	public String electionNoticeFormIforULB(Model model,
			@RequestParam(value = "districtId") String districtId, @RequestParam(value = "ulbCode") String ulbCode, @RequestParam(value = "electionCode") Long electionCode,
			@RequestParam(value = "ulbType") String ulbType,  @RequestParam(value = "wardNo") String wardNo,HttpServletRequest request, HttpServletResponse response) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String ulbCode1 = detailsByUserId.getUlgCode();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode1));
			String ulbType1 = (String) ulbDetails.get("ulb_type");
			
			String UlgTeluguName = withdrawalCandidatureNominationUlbRepository.getUlgTeluguName(districtId, ulbType1, ulbCode1);
			model.addAttribute("UlgTeluguName", UlgTeluguName);

			model.addAttribute("wardNo", wardNo);
			
			String districtName = userRepo.getTelDistrictName(districtId);
			model.addAttribute("districtName", districtName);
			
			String category = userRepo.getReservationForWardUlb(districtId,ulbCode1,wardNo);
			model.addAttribute("category", category);
			
			LocalDate currentDate = LocalDate.now();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
			String formattedDate = currentDate.format(formatter);
	        model.addAttribute("formatteddate",formattedDate);
	    	
	    	
	    	if(ulbType.split("_")[0].equals("1")) {
	    		List<Map<String, Object>> gpData = withdrawalCandidatureNominationUlbRepository.getGrampanchayatFormOneDataNp(districtId,ulbCode, 
						 electionCode,wardNo);
				model.addAttribute("gpData", gpData);
				}
				else if(ulbType.split("_")[0].equals("2")) {
					List<Map<String, Object>> gpData = withdrawalCandidatureNominationUlbRepository.getGrampanchayatFormOneDataMpl(districtId,ulbCode,
							 electionCode,wardNo);
					model.addAttribute("gpData", gpData);
					}
				else if(ulbType.split("_")[0].equals("3")) {
					List<Map<String, Object>> gpData = withdrawalCandidatureNominationUlbRepository.getGrampanchayatFormOneDataMc(districtId,ulbCode,
							 electionCode,wardNo);
					model.addAttribute("gpData", gpData);
					}
				else if(ulbType.split("_")[0].equals("4")) {
					List<Map<String, Object>> gpData = withdrawalCandidatureNominationUlbRepository.getGrampanchayatFormOneDataVmc(districtId,ulbCode, 
						 electionCode,wardNo);
					model.addAttribute("gpData", gpData);
					}
				else  {
					List<Map<String, Object>> gpData = withdrawalCandidatureNominationUlbRepository.getGrampanchayatFormOneDataGvmc(districtId,ulbCode,
							 electionCode,wardNo);
					model.addAttribute("gpData", gpData);
					}
	    	
	    	
	    	
	        String roUploadsignature = withdrawalCandidatureNominationUlbRepository.getUlbROUploadsignature(userId);
			model.addAttribute("roUploadsignature", roUploadsignature);
		        
		}catch(Exception e) {
			e.printStackTrace();
		}
		
				return "electionNoticeFormIForULB";
		
	}
	@GetMapping("/getElectionNotificationForROUlb")
	public String getElectionNotificationForROUlb(
			@ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto,
			HttpServletRequest request, Model model) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String districtId = detailsByUserId.getDistrict_id().toString();
			String ulbCode = detailsByUserId.getUlgCode();
			String wardNo = detailsByUserId.getWardId();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			
			List<Map<String, Object>> acList = userRepo.assemblyListULBByULG(districtId,
					ulbCode,Integer.parseInt(wardNo));

			model.addAttribute("districtId", districtId);
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
			model.addAttribute("assemblyList", acList);
			model.addAttribute("wardMem", "wardMem");
			model.addAttribute("ulbCode", ulbCode);

			model.addAttribute("ulbType",ulbDetails.get("ulb_type"));

			model.addAttribute("wardNo",wardNo);
			model.addAttribute("ulgName",ulbDetails.get("ulb_name"));
           
			if(ulbType.split("_")[0].equals("1")) {
				List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbNp(userId);
				model.addAttribute("electionsList", elections.get(0).get("election_code"));
				}
				else if(ulbType.split("_")[0].equals("2")) {
					List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbMpl(userId);
					model.addAttribute("electionsList", elections.get(0).get("election_code"));
					}
				else if(ulbType.split("_")[0].equals("3")) {
					List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbMc(userId);
					model.addAttribute("electionsList", elections.get(0).get("election_code"));
					}
				else if(ulbType.split("_")[0].equals("4")) {
					List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbVmc(userId);
					model.addAttribute("electionsList", elections.get(0).get("election_code"));
					}
				else  {
					List<Map<String, Object>> elections = userRepo.getElectionsForFormOneUlbGvmc(userId);
					model.addAttribute("electionsList", elections.get(0).get("election_code"));
					}
				
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "electionNotificationForROUlb";
	}

	@RequestMapping("getElectionNotificationForROUlbPdf")
	public void getElectionNotificationForROUlbPdf(
	        @ModelAttribute("withdrawalCandidatureNominationUlbDto") WithdrawalCandidatureNominationUlbDTO withdrawalCandidatureNominationUlbDto,
	        HttpServletRequest request, HttpServletResponse response) {
	    try {
	        List<Map<String, Object>> electionScheduleData = directElectionScheduleCustomRepo
	                .getElectionNotificationForROUlbPdf(withdrawalCandidatureNominationUlbDto.getDistrictId(),
	                        withdrawalCandidatureNominationUlbDto.getUlbType(), withdrawalCandidatureNominationUlbDto.getUlbCode());

	        // Ensure the list is not empty before accessing elements
	        if (electionScheduleData != null && !electionScheduleData.isEmpty()) {
	            String phase = (String) electionScheduleData.get(0).get("phase_no");
	            String electionMode = (String) electionScheduleData.get(0).get("mode_of_election");
	            String electionType = (String) electionScheduleData.get(0).get("election_type");
	            String post = (electionScheduleData.size() > 1) ? "Ward Member" : (String) electionScheduleData.get(0).get("post_type");

	            List<Map<String, Object>> electionScheduleForGpAnnexurePdf = new ArrayList<>();
	            List<Map<String, Object>> electionScheduleForGpAbstractPdf = new ArrayList<>();
	            
	            // Example usage of electionScheduleForGpAnnexurePdf (add logic if needed)
	            Map<String, Object> electionScheduleGP = new HashMap<>();
	            electionScheduleForGpAnnexurePdf.add(electionScheduleGP);
	        }
	    } catch (Exception e) {
	        e.printStackTrace(); // Consider using a logger instead of printing stack trace
	    }
	}

	@RequestMapping(value = "/fetchUlbFormAKWInfoForNpMpl")
	public String fetchUlbFormAKWInfoForNpMpl(HttpServletRequest request ,Model model, @RequestParam("slno") String slno) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			String districtId = detailsByUserId.getDistrict_id().toString();
			String ulbCode = detailsByUserId.getUlgCode();
			String wardNo = detailsByUserId.getWardId();
			Map<String, Object> ulbDetails = userRepo.getUlbDetails(Integer.parseInt(districtId),Integer.parseInt(ulbCode));
			String ulbType = (String) ulbDetails.get("ulb_type");
			
			String UlgEnglishName = withdrawalCandidatureNominationUlbRepository.getUlgEnglishName(districtId, ulbType, ulbCode);
			model.addAttribute("UlgEnglishName", UlgEnglishName);

			model.addAttribute("wardNo", wardNo);

			String  akwulbCandidateName =  withdrawalCandidatureNominationUlbService.akwUlbName(slno);
			model.addAttribute("akwulbCandidateName", akwulbCandidateName);

			String  akwcandidateDate =  withdrawalCandidatureNominationUlbService.akwDate(slno);
			model.addAttribute("akwcandidateDate", akwcandidateDate);

			String  akwcandidateTime =  withdrawalCandidatureNominationUlbService.akwTime(slno);
			model.addAttribute("akwcandidateTime", akwcandidateTime);

			String akwbywhomeformSubmit = withdrawalCandidatureNominationUlbService.akwbywhomeformSubmit(slno);
			model.addAttribute("akwbywhomeformSubmit", akwbywhomeformSubmit);
			String whomSubmitted="";
			if(akwbywhomeformSubmit.equals("1")) {
				whomSubmitted=	akwulbCandidateName;
			}
			if(akwbywhomeformSubmit.equals("2")) {
				whomSubmitted=withdrawalCandidatureNominationUlbRepository.whomSubmitted_proposer_ulb(slno);
			}
			if(akwbywhomeformSubmit.equals("3")) {
				whomSubmitted=withdrawalCandidatureNominationUlbRepository.whomSubmitted_election_agent_ward(detailsByUserId.getDistrict_id(),ulbType, ulbCode,wardNo);
			}
			model.addAttribute("whomSubmitted",whomSubmitted);
			
			String roUploadsignature = withdrawalCandidatureNominationUlbRepository.getUlbROUploadsignature(userId);
			model.addAttribute("roUploadsignature", roUploadsignature);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchUlbFormAKWInfoForNpMpl";
	}
		
		
	

}
