package cgg.gov.in.apsec.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.DirectVacancyForZPTCEntity;
import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.MasterMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyDLPOZPTCConfirmCustomRepository;
import cgg.gov.in.apsec.repo.VacancyDLPOZPTCConfirmRepository;
import cgg.gov.in.apsec.service.AddGPWardService;

@Controller
public class VacancyDLPOZPTCConfirmController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private AddGPWardService gpWardSevice;

	@Autowired
	private MasterMandalRepository mastermandalrepo;

	@Autowired
	private VacancyDLPOZPTCConfirmRepository vacancyDlpoZptcRepo;

	@Autowired
	private VacancyDLPOZPTCConfirmCustomRepository customRepo;

	@Autowired
	private DistrictRepo districtRepo;

	@GetMapping(value = "/vacancyDLPOForZPTCConfirm")
	public String vacancyDirectForZPTC(Model model,
			@ModelAttribute("directVacancyForZPTCEntity") DirectVacancyForZPTCEntity directVacancyForZPTCEntity,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();

			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);
				List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
				model.addAttribute("revenueDiv", revenueDiv);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDLPOForZPTCConfirm";
	}

	@RequestMapping(value = "/vacancyZptcGet", method = RequestMethod.POST)
	public String vacancyZptcGet(Model model,
			@ModelAttribute("directVacancyForZPTCEntity") DirectVacancyForZPTCEntity directVacancyForZPTCEntity,
			HttpServletRequest request, HttpServletResponse response,
			@RequestParam(name = "districtId", required = false) String districtId,
			@RequestParam(name = "revenueDivisionId", required = false) String revenueDivisionId,
			@RequestParam(name = "assemblyId", required = false) String assemblyId,
			@RequestParam(name = "gpcode", required = false) String gpcode,
			@RequestParam(name = "gpTerritorialConstituency", required = false) String gpTerritorialConstituency) {

		List<Map<String, Object>> vacZptcDtls = vacancyDlpoZptcRepo.vacancyZptcGet(districtId, revenueDivisionId,
				assemblyId, gpcode, gpTerritorialConstituency);
		if (districtId != null && !"0".equals(districtId)) {
			District district = districtRepo.findById(districtId).orElse(null);
			Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
			model.addAttribute("districtData", districtData);
			model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
			model.addAttribute("districtId", districtId);
			List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
			model.addAttribute("revenueDiv", revenueDiv);
		}
		model.addAttribute("vacZptcDtls", vacZptcDtls);
		if (vacZptcDtls.size() == 0) {
			model.addAttribute("getMsg", "true");
		}
		return "vacancyDLPOForZPTCConfirm";
	}

	@RequestMapping(value = "/vacancyDLPOZPTCConfirmSave", method = RequestMethod.POST)
	public String vacancyDLPOZPTCConfirmSave(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("assemblyId") String assemblyId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("gpcode") String gpcode,
			@RequestParam("gpTerritorialConstituency") String gpTerritorialConstituency,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDLPOZPTCConfirmSave(id, districtId, assemblyId, revenueDivisionId, gpcode,
				gpTerritorialConstituency, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details confirmed successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDLPOForZPTCConfirm";
	}

	@RequestMapping(value = "/vacancyDLPOZPTCRollbackSave", method = RequestMethod.POST)
	public String vacancyDLPOZPTCRollbackSave(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("assemblyId") String assemblyId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("gpcode") String gpcode,
			@RequestParam("gpTerritorialConstituency") String gpTerritorialConstituency,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDLPOZPTCRollbackSave(id, districtId, assemblyId, revenueDivisionId, gpcode,
				gpTerritorialConstituency, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details Rolledback successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDLPOForZPTCConfirm";
	}

	@GetMapping(value = "/getVacancyZPTCView")
	public String getVacancyZPTCView(Model model,
			@ModelAttribute("directVacancyForZPTCEntity") DirectVacancyForZPTCEntity directVacancyForZPTCEntity) {
		try {
			model.addAttribute("now", new Date());

			List<Map<String, Object>> VacancyZPTCCnfRoll = vacancyDlpoZptcRepo.vacancyZptcView();
			model.addAttribute("VacancyZPTCCnfRoll", VacancyZPTCCnfRoll);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDLPOForZPTCConfirmRollbackList";
	}
}