package cgg.gov.in.apsec.constants;

public class MessageConstant {

	public static final String MAPPING_GPFROMAC_FAILED = " Failed to Mapped Voters to GramPanchayat ! Please try again.";

	public static final String GP_DATA_SENT_FAILED = "Failed to Update!Please try again...";

	public static final String GP_WISE_VOTERS_CONFIRM_DATA_SUCCUSS = "MPDO Proposed Voter List Confirmed";

	public static final String GP_WISE_VOTERS_CONFIRM_DATA_FAILED = "AC Part Sl.No. is repeated in the voters list sent by the concerned <PERSON><PERSON><PERSON> for confirmation.  Please ROLLBACK and ask concerned <PERSON><PERSON><PERSON> to delete repeated AC Part Sl.No. and resend to DPO  again for confirmation.  After deleting repeated AC Part Sl.No. by concerned <PERSON><PERSON><PERSON>, and send modified list to DPO, then the software will allow for CONFIRMATION of voters by DPO ";

	public static final String ROLLBACK_DATA_SUCCUSS = "Updated Successfully....";

	public static final String USER_CREATE_SUCCUSS = "User successfully created.";
	public static final String USER_CREATE_FAILED = "User creation failed. Please try again..!";

	public static final String MAPPING_SUCCUSS = "AC parts successfully mapped to GP including village mapping.";

	public static final String SERVICE_VOTERS_MAPPING_SUCCESS = "Service voters mapped successfully..!!!";

	public static final String SERVICE_VOTERS_MAPPING_FAILURE = "Failed to map service voters...!!!";

	public static final String MAPPING_WARD_WISE_PS_NUMBERS_SUCCUSS = "Ward wise PS Numbers Mapped Successfully.";

	public static final String MAPPING_WARD_WISE_PS_NUMBERS_FAILED = "Ward wise PS Numbers Mapped failed. Please try again..!";

	public static final String DATA_MAPPED_MESSAGE = "Gram Panchayat-wise mapping of voters has already been completed.";
	
	public static final String SERVICE_VOTERS_DELETE_SUCCESS = "Service voters deleted successfully..!!!";

	public static final String SERVICE_VOTERS_DELETE_FAILURE = "Failed to delete service voters...!!!";

}
