package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.VacancyForMunicipalEntity;
import cgg.gov.in.apsec.pojo.VacancyForCDMADto;
import cgg.gov.in.apsec.pojo.VacancyForPresidentDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.VacancyForMayorOrDeputyMayorForCDMAApprovalService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyForMayorOrDeputyMayorForCDMAApprovalController {

	private static Logger logger = LoggerFactory.getLogger(VacancyForMayorOrDeputyMayorForCDMAApprovalController.class);

	@Autowired
	private UserRepository userRepo;

	@Autowired
	VacancyForMayorOrDeputyMayorForCDMAApprovalService vacancyForMayorOrDeputyMayorForCDMAApprovalService;

	@GetMapping(value = "/vacancyForMayorOrDeputyMayorForCDMAApproval")
	public String vacancyForMayorOrDeputyMayorForCDMAApproval(Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> ulbType = userRepo.getUlbType();
			
			
			List<Map<String, Object>> otherUlbType = ulbType.stream()
				    .filter(ulb -> "4_VMC".equals(ulb.get("value")) || "5_GVMC".equals(ulb.get("value")) || "3_Municipal Corporation".equals(ulb.get("value")))
				    .collect(Collectors.toList());
			model.addAttribute("ulbData", otherUlbType);

			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForMayor", "ULBListForMayor");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}

	@RequestMapping(value = "/getVacancyReportDataOfULB", method = RequestMethod.POST)
	public String getVacancyReportDataOfULB(Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "ulbTypeNew", required = false) String ulbType,
			HttpServletRequest request) {

			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> getVacancyReportDataOfULB = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBMayor(districtId, ulbType);
			model.addAttribute("getVacancyReportDataOfULB", getVacancyReportDataOfULB);

			if (getVacancyReportDataOfULB.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
		List<Map<String, Object>> otherUlbType = ulbTypeList.stream()
			    .filter(ulb -> "4_VMC".equals(ulb.get("value")) || "5_GVMC".equals(ulb.get("value")) || "3_Municipal Corporation".equals(ulb.get("value")))
			    .collect(Collectors.toList());
		model.addAttribute("ulbData", otherUlbType);
		model.addAttribute("ULBListForMayor", "ULBListForMayor");

		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}
	
	@RequestMapping(value = "/getVacancyReportDataOfULBChairperson", method = RequestMethod.POST)
	public String getVacancyReportDataOfULBChairperson(Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "ulbTypeNew", required = false) String ulbType,HttpServletRequest request) {

			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> getVacancyReportDataOfULBChairperson = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBChairperson(districtId, ulbType);
			model.addAttribute("getVacancyReportDataOfULBChairperson", getVacancyReportDataOfULBChairperson);

			if (getVacancyReportDataOfULBChairperson.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();

		// Filter only the required records dynamically
		List<Map<String, Object>> filteredUlbType = ulbTypeList.stream()
		    .filter(ulb -> "1_Nagar Panchayat".equals(ulb.get("value")) || "2_Municipality".equals(ulb.get("value")))
		    .collect(Collectors.toList());

		model.addAttribute("ulbData", filteredUlbType);

		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		model.addAttribute("ULBListForChairperson", "ULBListForChairperson");
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}

	@RequestMapping(value = "/vacancyMayorOrDeputyMayorCDMAApproval", method = RequestMethod.POST)
	public String vacancyMayorOrDeputyMayorCDMAApproval(Model model, HttpServletRequest request,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			RedirectAttributes redirectAttr) {
		logger.info("Enter into vacancyMayorOrDeputyMayorCDMAApproval method");
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			boolean isSaved = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.vacancyMayorOrDeputyMayorCDMAApproval(vacancyForCDMADto.getSelectedItems(), userId);
			if (isSaved) {
				model.addAttribute("saveSuccess", "Vacancy confirmation saved successfully.");
			} else {
				model.addAttribute("saveFailure", "Failed to save vacancy confirmation.");
			}
			redirectAttr.addFlashAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			if(vacancyForCDMADto.getUlbTypeNew().split("_")[0].equals("3") || 
					vacancyForCDMADto.getUlbTypeNew().split("_")[0].equals("4") || 
					vacancyForCDMADto.getUlbTypeNew().split("_")[0].equals("5")) {
						List<Map<String, Object>> getVacancyReportDataOfULB = vacancyForMayorOrDeputyMayorForCDMAApprovalService
								.getVacancyReportDataOfULBMayor(vacancyForCDMADto.getDistrictIdNew(), vacancyForCDMADto.getUlbTypeNew());
						model.addAttribute("getVacancyReportDataOfULB", getVacancyReportDataOfULB);

						if (getVacancyReportDataOfULB.size() == 0) {
							model.addAttribute("getMsg", "true");
						}

					List<Map<String, Object>> districtData = userRepo.findDistricts();
					model.addAttribute("districtData", districtData);

					List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
					List<Map<String, Object>> otherUlbType = ulbTypeList.stream()
						    .filter(ulb -> "4_VMC".equals(ulb.get("value")) || "5_GVMC".equals(ulb.get("value")) || "3_Municipal Corporation".equals(ulb.get("value")))
						    .collect(Collectors.toList());
					model.addAttribute("ulbData", otherUlbType);
					model.addAttribute("ULBListForMayor", "ULBListForMayor");

					model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			}
		} catch (Exception e) {
			logger.error("Error at vacancyMayorOrDeputyMayorCDMAApproval method: " + e.getMessage(), e);
		}
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}
	
	
	@RequestMapping(value = "/vacancyMayorOrDeputyChairpersonCDMAApproval", method = RequestMethod.POST)
	public String vacancyMayorOrDeputyChairpersonCDMAApproval(Model model, HttpServletRequest request,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			RedirectAttributes redirectAttr) {
		logger.info("Enter into vacancyMayorOrDeputyChairpersonCDMAApproval method");
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			boolean isSaved = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.vacancyMayorOrDeputyChairpersonCDMAApproval(vacancyForCDMADto.getSelectedItems(), userId);
			if (isSaved) {
				model.addAttribute("saveSuccess", "Vacancy confirmation saved successfully.");
			} else {
				model.addAttribute("saveFailure", "Failed to save vacancy confirmation.");
			}
			redirectAttr.addFlashAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
if(vacancyForCDMADto.getUlbTypeNew().split("_")[0].equals("3") || 
		vacancyForCDMADto.getUlbTypeNew().split("_")[0].equals("4") || 
		vacancyForCDMADto.getUlbTypeNew().split("_")[0].equals("5")) {
			List<Map<String, Object>> getVacancyReportDataOfULB = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBMayor(vacancyForCDMADto.getDistrictIdNew(), vacancyForCDMADto.getUlbTypeNew());
			model.addAttribute("getVacancyReportDataOfULB", getVacancyReportDataOfULB);

			if (getVacancyReportDataOfULB.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
		List<Map<String, Object>> otherUlbType = ulbTypeList.stream()
			    .filter(ulb -> "4_VMC".equals(ulb.get("value")) || "5_GVMC".equals(ulb.get("value")) || "3_Municipal Corporation".equals(ulb.get("value")))
			    .collect(Collectors.toList());
		model.addAttribute("ulbData", otherUlbType);
		model.addAttribute("ULBListForMayor", "ULBListForMayor");

		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
}
else {
		

		List<Map<String, Object>> getVacancyReportDataOfULBChairperson = vacancyForMayorOrDeputyMayorForCDMAApprovalService
				.getVacancyReportDataOfULBChairperson(vacancyForCDMADto.getDistrictIdNew(), vacancyForCDMADto.getUlbTypeNew());
		model.addAttribute("getVacancyReportDataOfULBChairperson", getVacancyReportDataOfULBChairperson);

		if (getVacancyReportDataOfULBChairperson.size() == 0) {
			model.addAttribute("getMsg", "true");
		}

	List<Map<String, Object>> districtData1 = userRepo.findDistricts();
	model.addAttribute("districtData", districtData1);

	List<Map<String, Object>> ulbTypeList1 = userRepo.getUlbType();

	// Filter only the required records dynamically
	List<Map<String, Object>> filteredUlbType1 = ulbTypeList1.stream()
	    .filter(ulb -> "1_Nagar Panchayat".equals(ulb.get("value")) || "2_Municipality".equals(ulb.get("value")))
	    .collect(Collectors.toList());

	model.addAttribute("ulbData", filteredUlbType1);

	model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
	model.addAttribute("ULBListForChairperson", "ULBListForChairperson");
}
	
		} catch (Exception e) {
			logger.error("Error at vacancyMayorOrDeputyMayorCDMAApproval method: " + e.getMessage(), e);
		}
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";

	}

	
	
	
	@GetMapping(value = "/vacancyWardForCDMAApproval")
	public String vacancyWardForCDMAApproval(Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> ulbType = userRepo.getUlbType();
			model.addAttribute("ulbData", ulbType);

			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForWard", "ULBListForWard");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}
	
	@RequestMapping(value = "/getVacancyReportDataOfULBWard", method = RequestMethod.POST)
	public String getVacancyReportDataOfULBWard(Model model,HttpServletRequest request,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "ulbTypeNew", required = false) String ulbType) {
		
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> getVacancyReportDataOfULBWard = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBWard(districtId, ulbType);
			model.addAttribute("getVacancyReportDataOfULBWard", getVacancyReportDataOfULBWard);

			if (getVacancyReportDataOfULBWard.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
		model.addAttribute("ulbData", ulbTypeList);
		model.addAttribute("ULBListForWard", "ULBListForWard");
		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}
	
	
	
	
	@RequestMapping(value = "/saveVacancyWardForCDMAApproval", method = RequestMethod.POST)
	public String saveVacancyWardForCDMAApproval(Model model, HttpServletRequest request,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			RedirectAttributes redirectAttr) {
		logger.info("Enter into vacancyWardForCDMAApproval method");
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			boolean isSaved = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.vacancyWardForCDMAApproval(vacancyForCDMADto.getSelectedItems(), userId);
			if (isSaved) {
				model.addAttribute("saveSuccess", "Vacancy confirmed successfully.");
			} else {
				model.addAttribute("saveFailure", "Failed to confirm vacancy.");
			}
			redirectAttr.addFlashAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForWard", "ULBListForWard");
			
			List<Map<String, Object>> getVacancyReportDataOfULBWard = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBWard(vacancyForCDMADto.getDistrictIdNew(), vacancyForCDMADto.getUlbTypeNew());
			model.addAttribute("getVacancyReportDataOfULBWard", getVacancyReportDataOfULBWard);

			if (getVacancyReportDataOfULBWard.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
		model.addAttribute("ulbData", ulbTypeList);
		model.addAttribute("ULBListForWard", "ULBListForWard");
		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		} catch (Exception e) {
			logger.error("Error at vacancyWardForCDMAApproval method: " + e.getMessage(), e);
		}
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";

	}
	
	
	
	@GetMapping(value = "/vacancyChairpersonForCDMAApproval")
	public String vacancyChairpersonForCDMAApproval(Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> ulbType = userRepo.getUlbType();

			// Filter only the required records dynamically
			List<Map<String, Object>> filteredUlbType = ulbType.stream()
			    .filter(ulb -> "1_Nagar Panchayat".equals(ulb.get("value")) || "2_Municipality".equals(ulb.get("value")))
			    .collect(Collectors.toList());

			model.addAttribute("ulbData", filteredUlbType);

			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForChairperson", "ULBListForChairperson");
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMayorOrDeputyMayorForCDMAApproval";
	}
	
	
	
	@GetMapping(value = "/vacancyULBForWardInSEC")
	public String vacancyULBForWardInSEC(HttpServletRequest request,Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto) {
		try {
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> ulbType = userRepo.getUlbType();
			model.addAttribute("ulbData", ulbType);

			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForWard", "ULBListForWard");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyULBForWardInSEC";
	}
	
	
	
	@RequestMapping(value = "/getVacancyWardSEC", method = RequestMethod.POST)
	public String getVacancyWardSEC(HttpServletRequest request,Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "ulbTypeNew", required = false) String ulbType) {

			List<Map<String, Object>> getVacancyReportDataOfULBWard = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBWard(districtId, ulbType);
			model.addAttribute("getVacancyReportDataOfULBWard", getVacancyReportDataOfULBWard);

			if (getVacancyReportDataOfULBWard.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
		model.addAttribute("ulbData", ulbTypeList);
		model.addAttribute("ULBListForWard", "ULBListForWard");
		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		String token = TokenUtil.generateToken(request.getSession());
		model.addAttribute("formToken", token);
		return "vacancyULBForWardInSEC";
	}
	
	
	@GetMapping(value = "/vacancyULBForChairpersonInSEC")
	public String vacancyULBForChairpersonInSEC(HttpServletRequest request,Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto) {
		try {
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> ulbType = userRepo.getUlbType();

			// Filter only the required records dynamically
			List<Map<String, Object>> filteredUlbType = ulbType.stream()
			    .filter(ulb -> "1_Nagar Panchayat".equals(ulb.get("value")) || "2_Municipality".equals(ulb.get("value")))
			    .collect(Collectors.toList());

			model.addAttribute("ulbData", filteredUlbType);

			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForChairperson", "ULBListForChairperson");
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyULBForWardInSEC";
	}
	
	
	@RequestMapping(value = "/getVacancyChairpersonSEC", method = RequestMethod.POST)
	public String getVacancyChairpersonSEC(HttpServletRequest request,Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "ulbTypeNew", required = false) String ulbType) {

			List<Map<String, Object>> getVacancyReportDataOfULBChairperson = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBChairperson  (districtId, ulbType);
			model.addAttribute("getVacancyReportDataOfULBChairperson", getVacancyReportDataOfULBChairperson);

			if (getVacancyReportDataOfULBChairperson.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();

		// Filter only the required records dynamically
		List<Map<String, Object>> filteredUlbType = ulbTypeList.stream()
		    .filter(ulb -> "1_Nagar Panchayat".equals(ulb.get("value")) || "2_Municipality".equals(ulb.get("value")))
		    .collect(Collectors.toList());

		model.addAttribute("ulbData", filteredUlbType);

		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		model.addAttribute("ULBListForChairperson", "ULBListForChairperson");
		String token = TokenUtil.generateToken(request.getSession());
		model.addAttribute("formToken", token);
		return "vacancyULBForWardInSEC";
	}
	
	
	@GetMapping(value = "/vacancyULBForMayorInSEC")
	public String vacancyULBForMayorInSEC(HttpServletRequest request,Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto) {
		try {
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> ulbType = userRepo.getUlbType();
			
			
			List<Map<String, Object>> otherUlbType = ulbType.stream()
				    .filter(ulb -> "4_VMC".equals(ulb.get("value")) || "5_GVMC".equals(ulb.get("value")) || "3_Municipal Corporation".equals(ulb.get("value")))
				    .collect(Collectors.toList());
			model.addAttribute("ulbData", otherUlbType);

			model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
			model.addAttribute("ULBListForMayor", "ULBListForMayor");
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyULBForWardInSEC";
	}
	
	
	@RequestMapping(value = "/getVacancyMayorSEC", method = RequestMethod.POST)
	public String getVacancyMayorSEC(HttpServletRequest request,Model model,
			@ModelAttribute("vacancyForCDMADto") VacancyForCDMADto vacancyForCDMADto,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "ulbTypeNew", required = false) String ulbType) {
		String token = TokenUtil.generateToken(request.getSession());
		model.addAttribute("formToken", token);
		
			List<Map<String, Object>> getVacancyReportDataOfULB = vacancyForMayorOrDeputyMayorForCDMAApprovalService
					.getVacancyReportDataOfULBMayor(districtId, ulbType);
			model.addAttribute("getVacancyReportDataOfULB", getVacancyReportDataOfULB);

			if (getVacancyReportDataOfULB.size() == 0) {
				model.addAttribute("getMsg", "true");
			}

		List<Map<String, Object>> districtData = userRepo.findDistricts();
		model.addAttribute("districtData", districtData);

		List<Map<String, Object>> ulbTypeList = userRepo.getUlbType();
		List<Map<String, Object>> otherUlbType = ulbTypeList.stream()
			    .filter(ulb -> "4_VMC".equals(ulb.get("value")) || "5_GVMC".equals(ulb.get("value")) || "3_Municipal Corporation".equals(ulb.get("value")))
			    .collect(Collectors.toList());
		model.addAttribute("ulbData", otherUlbType);
		model.addAttribute("ULBListForMayor", "ULBListForMayor");

		model.addAttribute("vacancyForCDMADto", vacancyForCDMADto);
		return "vacancyULBForWardInSEC";
	}
	
}
