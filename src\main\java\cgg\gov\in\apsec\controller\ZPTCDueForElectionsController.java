package cgg.gov.in.apsec.controller;

import java.security.Principal;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import cgg.gov.in.apsec.constants.DataBaseConstants;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.ZPTCDueForElectionsDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.ZPTCDueForElectionsRepository;

@Controller
public class ZPTCDueForElectionsController {

	private static Logger logger = LoggerFactory.getLogger(MPTCDueForElectionsController.class);

	@Autowired
	private ZPTCDueForElectionsRepository zptcDueForElectionsRepository;
	
	@Autowired
	private UserRepository userRepo;

	String electionType = null;
	String vacancyDate = null;

	@GetMapping(value = "/zptcDueForElections")
	public String mptcForElections(Model model,
			@ModelAttribute("zptcDueForElectionsDTO") ZPTCDueForElectionsDTO zptcDueForElectionsDTO,
			Principal principal) {
		try {
			User detailsByUserId = userRepo.getDetailsByUserId(principal.getName());
			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(detailsByUserId.getUserId());
			model.addAttribute("electionsList", elections);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "zptcDueForElections";
	}

	@PostMapping(value = "/getZptcDueForElections")
	public String getZptcDueForElections(Model model,
			@ModelAttribute("zptcDueForElectionsDTO") ZPTCDueForElectionsDTO zptcDueForElectionsDTO,
			Principal principal) {
		try {
			User detailsByUserId = userRepo.getDetailsByUserId(principal.getName());
			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(detailsByUserId.getUserId());
			model.addAttribute("electionsList", elections);
			electionType = zptcDueForElectionsDTO.getElectionType();
			vacancyDate = zptcDueForElectionsDTO.getDate();
			if (DataBaseConstants.CASUAL_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> zptcDueForElectionList = zptcDueForElectionsRepository
						.getCasualZptcDueForElections(zptcDueForElectionsDTO.getDate());
				model.addAttribute("zptcDueForElectionList", zptcDueForElectionList);
			}
			if (electionType.equals(DataBaseConstants.ORDINARY_ELECTION_TYPE_ZPP)) {
				List<Map<String, Object>> zptcDueForElectionList = zptcDueForElectionsRepository
						.getZptcDueForElections(zptcDueForElectionsDTO.getDate());
				model.addAttribute("zptcDueForElectionList", zptcDueForElectionList);
			}
			
			model.addAttribute("selectedElection", zptcDueForElectionsDTO.getElectionType());
			model.addAttribute("selectedDate", zptcDueForElectionsDTO.getDate());
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "zptcDueForElections";
	}

	@RequestMapping("/distZPTCSElection")
	public String distZPTCSElection(
			@ModelAttribute("zptcDueForElectionsDTO") ZPTCDueForElectionsDTO zptcDueForElectionsDTO,
			@RequestParam(value = "dist") String dist, Model model, Principal principal) {
		logger.info("distZPTCSElection..");
		try {
			User detailsByUserId = userRepo.getDetailsByUserId(principal.getName());
			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(detailsByUserId.getUserId());
			model.addAttribute("electionsList", elections);
			if (DataBaseConstants.CASUAL_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> distData = zptcDueForElectionsRepository.getCasualDistrictNames(vacancyDate);
				model.addAttribute("distData", distData);
			}
			if (DataBaseConstants.ORDINARY_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> distData = zptcDueForElectionsRepository.getDistrictNames(vacancyDate);
				model.addAttribute("distData", distData);
			}
			
			model.addAttribute("selectedElection", zptcDueForElectionsDTO.getSelectedElection());
			model.addAttribute("selectedDate", zptcDueForElectionsDTO.getSelectedDate());
		} catch (Exception e) {
			logger.error("Error at distZPTCSElection.." + e.getMessage());
		}
		return "zptcDueForElections";

	}

	@RequestMapping("/distMandalZPTCElections")
	public String distMandalZPTCElections(
			@ModelAttribute("zptcDueForElectionsDTO") ZPTCDueForElectionsDTO zptcDueForElectionsDTO,
			@RequestParam(value = "distId") String distId, @RequestParam(value = "mandal") String mandal, Model model,
			Principal principal) {
		logger.info("distMandalZPTCElections..");
		try {
			User detailsByUserId = userRepo.getDetailsByUserId(principal.getName());
			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(detailsByUserId.getUserId());
			model.addAttribute("electionsList", elections);
			if (DataBaseConstants.CASUAL_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> distmandalData = zptcDueForElectionsRepository.getCasualDistrictMandalNames(vacancyDate,distId);
				model.addAttribute("distmandalData", distmandalData);
			}
			if (DataBaseConstants.ORDINARY_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> distmandalData = zptcDueForElectionsRepository.getDistrictMandalNames(vacancyDate,distId);
				model.addAttribute("distmandalData", distmandalData);
			}
			model.addAttribute("selectedElection", zptcDueForElectionsDTO.getSelectedElection());
			model.addAttribute("selectedDate", zptcDueForElectionsDTO.getSelectedDate());
		} catch (Exception e) {
			logger.error("Error at distMandalZPTCElections.." + e.getMessage());
		}
		return "zptcDueForElections";
	}
	
	
	@RequestMapping("/distMandalZPTCElectionss")
	public String distMandalZPTCElectionss(
			@ModelAttribute("zptcDueForElectionsDTO") ZPTCDueForElectionsDTO zptcDueForElectionsDTO,
			@RequestParam(value = "distId") String distId, @RequestParam(value = "mandal") String mandal, Model model,
			Principal principal) {
		logger.info("distMandalZPTCElections..");
		try {
			User detailsByUserId = userRepo.getDetailsByUserId(principal.getName());
			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(detailsByUserId.getUserId());
			model.addAttribute("electionsList", elections);
			model.addAttribute("distId", detailsByUserId.getDistrict_id());
			if (DataBaseConstants.CASUAL_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> distmandalzptcData = zptcDueForElectionsRepository.getCasualDistrictMandalZptcNames(vacancyDate,distId,mandal);
				model.addAttribute("distmandalzptcData", distmandalzptcData);
			}
			if (DataBaseConstants.ORDINARY_ELECTION_TYPE_ZPP.equals(electionType)) {
				List<Map<String, Object>> distmandalzptcData = zptcDueForElectionsRepository.getDistrictMandalZptcNames(vacancyDate,distId,mandal);
				model.addAttribute("distmandalzptcData", distmandalzptcData);
			}
			model.addAttribute("selectedElection", zptcDueForElectionsDTO.getSelectedElection());
			model.addAttribute("selectedDate", zptcDueForElectionsDTO.getSelectedDate());
		} catch (Exception e) {
			logger.error("Error at distMandalZPTCElections.." + e.getMessage());
		}
		return "zptcDueForElections";
	}
}