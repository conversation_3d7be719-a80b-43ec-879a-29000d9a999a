package cgg.gov.in.apsec.asdvoter.repo;

import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import cgg.gov.in.apsec.modal.User;

@Repository
public interface ULBWiseASDVotersRepo extends JpaRepository<User, Long> {

	@Query(value = "SELECT CAST(ps_no AS TEXT) AS pollingstation_id, ps_no " +
            "FROM master_sec_polingstation_ulb " +
            "WHERE district_id = :distId " +
            "AND ulg_code = :ulbType " +
            "AND ward = :wardNo", 
    nativeQuery = true)
List<Map<String, Object>> getPSDetailsULBType(@Param("distId") String distId, 
                                           @Param("ulbType") String ulbType, 
                                           @Param("wardNo") String wardNo);


	@Modifying
	@Transactional
	@Query(value = "INSERT INTO public.list_of_ulb_asd_voters_pswise (district_id, district_name, ulb_type, pollingstation_no, voter_in_ward_er, "
			+ "category, inserted_by, inserted_time, inserted_ip) VALUES (:districtId, :districtName, :ulbType, :pollingstationNo, :voterInGpEr, "
			+ ":category, :insertedBy, now(), :insertedIp)", nativeQuery = true)
	Integer saveULBWiseVoterDetails(@Param("districtId") String districtId, @Param("districtName") String districtName,
			@Param("ulbType") String ulbType, @Param("pollingstationNo") String pollingstationNo,
			@Param("voterInGpEr") String voterInGpEr, @Param("category") String category,
			@Param("insertedBy") String insertedBy, @Param("insertedIp") String insertedIp);

	@Query(value = "select voter_in_ward_er,category,ulb_type,pollingstation_no from list_of_ulb_asd_voters_pswise where district_id=:district_id AND ulb_type=:ulbType  ", nativeQuery = true)
	List<Object[]> getListofASDVoterPSWise(@Param("district_id") String district_id, @Param("ulbType") String ulbType);

	@Query(value = "SELECT category, COUNT(*) AS count from list_of_ulb_asd_voters_pswise where district_id=:district_id and "
			+ "ulb_type=:ulbType GROUP BY category", nativeQuery = true)
	List<Map<String, Object>> getULBWiseASDVotersCountBycategoary(@Param("district_id") String district_id,
			@Param("ulbType") String ulbType);

	@Query(value = "SELECT from_slno as from_serialno, to_slno as to_serialno FROM master_sec_polingstation_ulb "
			+ "WHERE ulg_code = :ulbCode AND district_id = :districtId "
			+ "AND ps_no = :psNo AND ward = :ward", nativeQuery = true)
	List<Map<String, Object>> getFromSlnoTOslNoForULBWiseVoer(@Param("ulbCode") String ulbCode,
			@Param("districtId") String districtId, @Param("psNo") String psNo, @Param("ward") String ward);
	
	@Query(value = "SELECT  COUNT(*) AS count FROM asd_voters_ward_pswise " 
	        + "WHERE district_id = :district_id AND ulb_name = :ulbName AND pollingstation_no = :psNo "
	        + "AND voter_in_ward_er = :voterinwardEr AND ward_id = :wardNum AND is_active = true and ulb_code=:ulbCode ", nativeQuery = true)
	int isVoterDuplicateInWardPSWise (@Param("district_id") String district_id,
	        @Param("ulbName") String ulbName,
	        @Param("psNo") String psNo,
	        @Param("voterinwardEr") String voterinwardEr,
	        @Param("wardNum") String wardNum,@Param("ulbCode") int ulbCode);


}
