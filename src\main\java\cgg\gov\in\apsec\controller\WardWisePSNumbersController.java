package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.constants.MessageConstant;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.MappingGPfromAssemblyVotersDTO;
import cgg.gov.in.apsec.repo.ElectionSarpanchRuralPsAllotmentCustomRepository;
import cgg.gov.in.apsec.repo.UserRepository;

@Controller
public class WardWisePSNumbersController {

	private static Logger log = LoggerFactory.getLogger(WardWisePSNumbersController.class);

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private ElectionSarpanchRuralPsAllotmentCustomRepository msSqlRepo;

	@RequestMapping(value = "/wardWisePSNumbers")
	public ModelAndView wardWisePSNumbers(HttpServletRequest request,
			@ModelAttribute("mappingGPfromAssemblyVotersDTO") MappingGPfromAssemblyVotersDTO mappingGPfromAssemblyVotersDTO) {
		log.info("Enter into wardWisePSNumbers() WardWisePSNumbersController::");
		ModelAndView mav = new ModelAndView();
		List<Map<String, Object>> districtsList = null, revenueDivisionList = null, mandalList = null;
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String distCode = detailsByUserId.getDistrict_id();
			String revenueDivisionCode = detailsByUserId.getRevenueDivisionId();
			String mandalCode = detailsByUserId.getMandalId();

			districtsList = userRepo.findDistricts();
			mav.addObject("districtsList", districtsList);

			revenueDivisionList = userRepo.findRevenueDivisionBasedOnDistrict(distCode);
			mav.addObject("revenueDivisionList", revenueDivisionList);

			mandalList = userRepo.findMandalBasedOnRevenueDivision(distCode, revenueDivisionCode);
			mav.addObject("mandalList", mandalList);

			mappingGPfromAssemblyVotersDTO.setDistrictId(distCode);
			mappingGPfromAssemblyVotersDTO.setRevenueDivisionId(revenueDivisionCode);
			mappingGPfromAssemblyVotersDTO.setMandalId(mandalCode);

			List<Map<String, Object>> assemblyList = userRepo.assemblyList(distCode);
			mav.addObject("assemblyList", assemblyList);

			List<Map<String, Object>> grampanchayatList = userRepo.gramPanchayatList(distCode, revenueDivisionCode,
					mandalCode);
			mav.addObject("grampanchayatList", grampanchayatList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		mav.addObject("mappingGPfromAssemblyVotersDTO", mappingGPfromAssemblyVotersDTO);
		mav.setViewName("wardWisePSNumbers");
		return mav;
	}

	@RequestMapping(value = "/saveWardWisePSNumbers")
	public String saveWardWisePSNumbers(RedirectAttributes redirectAttr,
			@ModelAttribute("mappingGPfromAssemblyVotersDTO") MappingGPfromAssemblyVotersDTO mappingGPfromAssemblyVotersDTO) {
		log.info("Enter into saveWardWisePSNumbers() WardWisePSNumbersController::");
		try {
			int count = msSqlRepo.callWardWisePSNumbersProc(mappingGPfromAssemblyVotersDTO);
			if (count > 0) {
				redirectAttr.addFlashAttribute("successMessage", MessageConstant.MAPPING_WARD_WISE_PS_NUMBERS_SUCCUSS);
			} else {
				redirectAttr.addFlashAttribute("failureMessage", MessageConstant.MAPPING_WARD_WISE_PS_NUMBERS_FAILED);
			}
		} catch (Exception e) {
			redirectAttr.addFlashAttribute("failureMessage", MessageConstant.MAPPING_WARD_WISE_PS_NUMBERS_FAILED);
			e.printStackTrace();
		}
		mappingGPfromAssemblyVotersDTO.setAssemblyId("0");
		mappingGPfromAssemblyVotersDTO.setGrampanchayatId("0");
		mappingGPfromAssemblyVotersDTO.setWardId("0");
		redirectAttr.addFlashAttribute("mappingGPfromAssemblyVotersDTO", mappingGPfromAssemblyVotersDTO);
		return "redirect:/wardWisePSNumbers";
	}
}