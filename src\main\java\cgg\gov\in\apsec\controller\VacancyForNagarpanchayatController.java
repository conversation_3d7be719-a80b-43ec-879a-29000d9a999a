	package cgg.gov.in.apsec.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyForNagarpanchayatEntity;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyForNagarpanchayatsRepository;
import cgg.gov.in.apsec.service.UrbanRITBPOTPSScrutinyService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyForNagarpanchayatController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private VacancyForNagarpanchayatsRepository vacancyRepo;
	
	@Autowired
	UrbanRITBPOTPSScrutinyService urbanRITBPOTPSScrutinyService;

	@GetMapping(value = "/vacancyForNagarpanchayatAndMunicipalities")
	public String vacancyForNagarpanchayatAndMunicipalities(Model model,
			@ModelAttribute("vacancyForNagarpanchayatEntity") VacancyForNagarpanchayatEntity vacancyForNagarpanchayatEntity,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			List<Map<String, Object>> data = new ArrayList<>();
			

			model.addAttribute("districtId", districtId);
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));

			if (detailsByUserId.getConstituency_code() != null && !detailsByUserId.getConstituency_code().equals("0")) {
				model.addAttribute("assemblyId", detailsByUserId.getConstituency_code());
				String assemblyName = userRepo.getAssemblyNameULB(detailsByUserId.getDistrict_id(),
						detailsByUserId.getConstituency_code());
				model.addAttribute("assemblyName", assemblyName);
				List<Map<String, Object>> ulgList = userRepo.getULGList(detailsByUserId.getDistrict_id(),
						detailsByUserId.getConstituency_code());
				model.addAttribute("ulgList", ulgList);
			} else {
				List<Map<String, Object>> assembyList = userRepo.assemblyListULBByULG(detailsByUserId.getDistrict_id(),
						detailsByUserId.getUlgCode());
				model.addAttribute("assemblyConstituencyList", assembyList);
				model.addAttribute("wardNo", detailsByUserId.getWardId());
				model.addAttribute("ulgId", detailsByUserId.getUlgCode());
				model.addAttribute("ulgName",
						userRepo.getULGName(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode()));
				List<Map<String, Object>> getULGWardCount = userRepo.getULGWardCount(detailsByUserId.getDistrict_id(),
						detailsByUserId.getUlgCode());
				model.addAttribute("ulgWardsNo", getULGWardCount);
				
				if (detailsByUserId.getUlgCode() != null) {
					String ulgType = userRepo.getUlgType(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode());
					Map<String, Object> ulgData = urbanRITBPOTPSScrutinyService.getUlgData(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(), ulgType);
					model.addAttribute("ulbName", ulgData.get("ulg_name"));
					model.addAttribute("noOfWards", ulgData.get("no_of_wards"));
					model.addAttribute("ulbType", ulgType);
					model.addAttribute("ulgCode", ulgData.get("ulg_code"));
					if (ulgType.equals("1_Nagar Panchayat/Municipality")) {
						data = urbanRITBPOTPSScrutinyService.getULGWardsDataForNP(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(),
								"1_Nagar Panchayat/Municipality");
					} else {
						data = urbanRITBPOTPSScrutinyService.getULGWardsDataForMC(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(),
								"2_Municipal Corporation");
						model.addAttribute("vacancyForMunicipalEntity", data);
					}
					
				}
			}
			model.addAttribute("vacancyForNagarpanchayatEntity", vacancyForNagarpanchayatEntity);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForNagarpanchayatAndMunicipalities";
	}

	@PostMapping(value = "/vacancyForNagarpanchayatAndMunicipalitiesSave")
	public String vacancyForNagarpanchayatAndMunicipalitiesSave(Model model,
			@ModelAttribute("vacancyForNagarpanchayatEntity") VacancyForNagarpanchayatEntity vacancyForNagarpanchayatEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			vacancyForNagarpanchayatEntity.setUpdatedBy(userId);
			vacancyForNagarpanchayatEntity.setSubmittedByUser(userId);

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForNagarpanchayatEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;
			if (vacancyForNagarpanchayatEntity.getVacancyFillDatee() != null
					&& !vacancyForNagarpanchayatEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForNagarpanchayatEntity.getVacancyFillDatee());
				vacancyForNagarpanchayatEntity.setVacancyFillDate(dateOfFilling);
			}
			vacancyForNagarpanchayatEntity.setDateOfOccurrence(parsedDate);
			vacancyForNagarpanchayatEntity.setVacancyFillDate(dateOfFilling);

			vacancyRepo.save(vacancyForNagarpanchayatEntity);
			redirectAttr.addFlashAttribute("msg", "Vacancy for Nagarpanchayat & Municipalities Added Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Add  ! Please try again....");
		}
		return "redirect:/vacancyForNagarpanchayatAndMunicipalities";
	}

	@GetMapping(value = "/getVacancyForNagarpanchayatAndMunicipalities")
	public String getVacancyForNagarpanchayatAndMunicipalities(Model model,
			@ModelAttribute("vacancyForNagarpanchayatEntity") VacancyForNagarpanchayatEntity vacancyForNagarpanchayatEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);

			model.addAttribute("now", new Date());

			List<Map<String, Object>> VacancyForNagarpanchayat = vacancyRepo.getVacancyDetailsByDistrict(userId);
			model.addAttribute("VacancyForNagarpanchayat", VacancyForNagarpanchayat);

			List<Map<String, Object>> getULGWardCount = userRepo.getULGWardCount(detailsByUserId.getDistrict_id(),
					detailsByUserId.getUlgCode());
			model.addAttribute("ulgWardsNo", getULGWardCount);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForNagarpanchayatList";
	}

	@PostMapping(value = "/vacancyForNagarpanchayatAndMunicipalitiesUpdateForm")
	public String vacancyForNagarpanchayatAndMunicipalitiesUpdateForm(Model model, HttpServletRequest request,
			@ModelAttribute("vacancyForNagarpanchayatEntity") VacancyForNagarpanchayatEntity vacancyForNagarpanchayatEntity
			 ) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			int id = vacancyForNagarpanchayatEntity.getId();
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			Date utilDate3 = null;
			String dateText3 = null;

			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);
			
			List<Map<String, Object>> assemblyList = userRepo.assemblyList(detailsByUserId.getDistrict_id());
			model.addAttribute("assemblyList", assemblyList);

			List<Map<String, Object>> VacancyForNagarpanchayatUpdate = vacancyRepo.getVacancyUpdateForm(id);
			if (!VacancyForNagarpanchayatUpdate.isEmpty()) {

				Map<String, Object> firstRow = VacancyForNagarpanchayatUpdate.get(0);

				String districtid = (String) firstRow.get("district_id");
				vacancyForNagarpanchayatEntity.setDistrictId(districtid);
				model.addAttribute("district_id", districtid);

				String distName = userRepo.getDistrictName(districtid);
				model.addAttribute("districtName", distName);

				String assemblyId = (String) firstRow.get("assembly_id");
				vacancyForNagarpanchayatEntity.setAssemblyId(assemblyId);
				model.addAttribute("assembly_id", assemblyId);

				String assemblyName = vacancyRepo.getAssemblyConstituencyName(districtid, assemblyId);
				model.addAttribute("assemblyName", assemblyName);

				String wardNo = (String) firstRow.get("ward_no");
				vacancyForNagarpanchayatEntity.setWardNo(wardNo);
				model.addAttribute("ward_no", wardNo);

				String officeName = (String) firstRow.get("office_name");
				vacancyForNagarpanchayatEntity.setOfficeName(officeName);
				model.addAttribute("office_name", officeName);

				String name = (String) firstRow.get("ulb_name");
				vacancyForNagarpanchayatEntity.setUlbName(name);
				model.addAttribute("ulb_name", name);

				model.addAttribute("ulgName",
						userRepo.getULGName(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode()));
				List<Map<String, Object>> getULGWardCount = userRepo.getULGWardCount(detailsByUserId.getDistrict_id(),
						detailsByUserId.getUlgCode());
				model.addAttribute("ulgWardsNo", getULGWardCount);

				String reason = (String) firstRow.get("reason_vacancy");
				vacancyForNagarpanchayatEntity.setReasonVacancy(reason);
				model.addAttribute("reason_vacancy", reason);

				String electedName = (String) firstRow.get("elected_representative_name");
				vacancyForNagarpanchayatEntity.setElectedRep(electedName);
				model.addAttribute("elected_representative_name", electedName);

				String remarks = (String) firstRow.get("remarks");
				vacancyForNagarpanchayatEntity.setRemarks(remarks);
				model.addAttribute("remarks", remarks);

				String any1 = (String) firstRow.get("any_vacancy");
				vacancyForNagarpanchayatEntity.setAnyVacancy(any1);
				model.addAttribute("any_vacancy", any1);

				String anyImpediment = (String) firstRow.get("any_impediment");
				vacancyForNagarpanchayatEntity.setAnyImpediment(anyImpediment);
				model.addAttribute("any_impediment", anyImpediment);

				String chairpersonWardId = (String) firstRow.get("chairperson_ward_id");
				vacancyForNagarpanchayatEntity.setChairpersonWardId(chairpersonWardId);
				model.addAttribute("chairperson_ward_id", chairpersonWardId);
				
				String ulbType = (String) firstRow.get("ulb_type");
				vacancyForNagarpanchayatEntity.setUlbType(ulbType);
				model.addAttribute("ulbType", ulbType);
				
				String ulbCode = (String) firstRow.get("ulb_code");
				vacancyForNagarpanchayatEntity.setUlgCode(ulbCode);
				model.addAttribute("ulgCode", ulbCode);

				java.sql.Date sqlDate = (java.sql.Date) firstRow.get("date_of_occurrence");
				java.sql.Date sqlDate3 = (java.sql.Date) firstRow.get("vacancy_fill_date");

				Date utilDate = new Date(sqlDate.getTime());

				SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
				String dateText = dateFormat.format(utilDate);

				if (sqlDate3 != null) {
					utilDate3 = new Date(sqlDate3.getTime());
					dateText3 = dateFormat.format(utilDate3);
				}

				vacancyForNagarpanchayatEntity.setDateOfOccurrence(utilDate);
				vacancyForNagarpanchayatEntity.setVacancyFillDate(utilDate3);

				model.addAttribute("date_of_occurrence", dateText);
				model.addAttribute("vacancy_fill_date", dateText3);

				model.addAttribute("firstRow", firstRow);
			}
			model.addAttribute("populate", "populate");
			model.addAttribute("id", id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForNagarpanchayatAndMunicipalities";
	}

	@Transactional
	@PostMapping(value = "/UpdateVacancyForNagarpanchayatAndMunicipalities")
	public String UpdateVacancyForNagarpanchayatAndMunicipalities(Model model,
			@ModelAttribute("vacancyForNagarpanchayatEntity") VacancyForNagarpanchayatEntity vacancyForNagarpanchayatEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			String districtId = vacancyForNagarpanchayatEntity.getDistrictId();
			String officeName = vacancyForNagarpanchayatEntity.getOfficeName();
			String assemblyId = vacancyForNagarpanchayatEntity.getAssemblyId();
			String reason = vacancyForNagarpanchayatEntity.getReasonVacancy();
			String remarks = vacancyForNagarpanchayatEntity.getRemarks();
			String eleRep = vacancyForNagarpanchayatEntity.getElectedRep();

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForNagarpanchayatEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;
			if (vacancyForNagarpanchayatEntity.getVacancyFillDatee() != null
					&& !vacancyForNagarpanchayatEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForNagarpanchayatEntity.getVacancyFillDatee());
			}
			vacancyForNagarpanchayatEntity.setDateOfOccurrence(parsedDate);
			vacancyForNagarpanchayatEntity.setVacancyFillDate(dateOfFilling);
			if (dateOfFilling != null) {
				vacancyRepo.updateVacancyForNagarpanchayat(districtId, officeName, assemblyId,
						vacancyForNagarpanchayatEntity.getWardNo(), vacancyForNagarpanchayatEntity.getUlbName(), reason,
						parsedDate, dateOfFilling, remarks, vacancyForNagarpanchayatEntity.getAnyVacancy(), userId,
						vacancyForNagarpanchayatEntity.getAnyImpediment(),
						vacancyForNagarpanchayatEntity.getChairpersonWardId(), eleRep,userId, id);
			} else {
				vacancyRepo.updateVacancyForNagarpanchayatWithoutFillingDate(districtId, officeName, assemblyId,
						vacancyForNagarpanchayatEntity.getWardNo(), vacancyForNagarpanchayatEntity.getUlbName(), reason,
						parsedDate, remarks, vacancyForNagarpanchayatEntity.getAnyVacancy(), userId,
						vacancyForNagarpanchayatEntity.getAnyImpediment(),
						vacancyForNagarpanchayatEntity.getChairpersonWardId(), eleRep,userId, id);
			}

			redirectAttr.addFlashAttribute("msg",
					"Vacancy for Nagarpanchayat & Municipalities Updated Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Update  ! Please try again....");
		}
		return "redirect:/getVacancyForNagarpanchayatAndMunicipalities";
	}

	@Transactional
	@PostMapping(value = "/deleteVacancyForNagarpanchayatAndMunicipalities")
	public String deleteVacancyForNagarpanchayatAndMunicipalities(RedirectAttributes redirectAttr,
			@ModelAttribute("vacancyForNagarpanchayatEntity") VacancyForNagarpanchayatEntity vacancyForNagarpanchayatEntity) {
		try {
			int id =vacancyForNagarpanchayatEntity.getId(); 
			vacancyRepo.deleteByIdNative(id);
			redirectAttr.addFlashAttribute("msg",
					"Vacancy for Nagarpanchayat & Municipalities Deleted Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Delete  ! Please try again....");
		}
		return "redirect:/getVacancyForNagarpanchayatAndMunicipalities";
	}

	@RequestMapping(value = "/getULBNames", method = RequestMethod.GET)
	public @ResponseBody String getULBNames(@RequestParam(name = "district_id", required = false) String district_id,
			@RequestParam(name = "assemblyId", required = false) String assemblyId) {
		StringBuilder mainData = new StringBuilder();
		List<Map<String, Object>> ULBNamesList = userRepo.getULBNamesList(district_id, assemblyId);
		for (Map<String, Object> map : ULBNamesList) {
			mainData.append("<option value='" + map.get("ulg_code") + "'>" + map.get("ulg_name") + "</option>");
		}
		return mainData.toString();
	}

	@RequestMapping(value = "/getULBNamesDistrict", method = RequestMethod.GET)
	public @ResponseBody String getULBNamesDistrict(
			@RequestParam(name = "district_id", required = false) String district_id) {

		StringBuilder mainData = new StringBuilder();
		List<Map<String, Object>> ULBNamesList = userRepo.getULBNamesDistrict(district_id);

		for (Map<String, Object> map : ULBNamesList) {
			mainData.append("<option value='" + map.get("ulg_code") + "'>" + map.get("ulg_name") + "</option>");
		}
		return mainData.toString();
	}
}