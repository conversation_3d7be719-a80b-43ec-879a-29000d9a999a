package cgg.gov.in.apsec.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.VacancyGPWardSelectedItemsList;
import cgg.gov.in.apsec.pojo.VacancyReportForGPWardApprovalDTO;
import cgg.gov.in.apsec.repo.AddMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.AddMandalService;
import cgg.gov.in.apsec.service.VacancyReportForGPWardApprovalService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyReportForGPWardApprovalController {

	@Autowired
	private AddMandalRepository addMandalRepository;
	@Autowired
	private VacancyReportForGPWardApprovalService vacancyReportForGPWardApprovalService;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private AddMandalService addMandalService;

//	@Autowired
//	private GPWiseUpdatedVotersRepository gPWiseUpdated;

//	@Autowired
//	private DirectVacancyForZPTCRepository directVacancyZptcRepo;

	@GetMapping("/getVacancyReportApprovalForGPWard")
	public String getVacancyReportApprovalForGPWard(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			HttpServletRequest request) {
		try {
			
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id().toString();
			String revenueDivisionId = detailsByUserId.getRevenueDivisionId().toString();

			Map<String, Object> rdData = addMandalService.findRevenueDivisionsByDistRId(Integer.parseInt(districtId),
					Integer.parseInt(revenueDivisionId));
			model.addAttribute("rdData", rdData);

			List<Map<String, Object>> districtData = userRepo.findDistrictsByCode(Integer.parseInt(districtId));
			model.addAttribute("districtName", districtData.get(0).get("district_name"));
			model.addAttribute("districtId", districtData.get(0).get("district_id"));

			List<Map<String, Object>> assemblyConstituencyList = userRepo.assemblyList(districtId);
			model.addAttribute("assemblyConstituencyList", assemblyConstituencyList);

			List<Map<String, Object>> mandals = userRepo.findMandalBasedOnRevenueDivision(districtId,
					revenueDivisionId);
			model.addAttribute("mandals", mandals);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyReportApprovalForGPWard";
	}

	@PostMapping("/getVacancyReportDataOfGPWard")
	public String getVacancyReportDataOfGPWard(
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String districtId = vacancyReportForGPWardApprovalDTO.getDistrictIdNew();
			String revenueDivisionId = vacancyReportForGPWardApprovalDTO.getRevenueDivisionIdNew();
			String assemblyId = vacancyReportForGPWardApprovalDTO.getAssemblyId();
			String mandalId = vacancyReportForGPWardApprovalDTO.getMandalIdNew();
			String gpcode = vacancyReportForGPWardApprovalDTO.getGpcode();
			String wardId = vacancyReportForGPWardApprovalDTO.getWardId();

			/*
			 * List<Map<String, Object>> assemblyConstituencyList =
			 * userRepo.assemblyList(districtId);
			 * redirectAttr.addFlashAttribute("assemblyConstituencyListNew",
			 * assemblyConstituencyList);
			 */

			/*
			 * List<Map<String, Object>> mandals =
			 * userRepo.findMandalBasedOnRevenueDivision(districtId, revenueDivisionId);
			 * redirectAttr.addFlashAttribute("mandalsNew", mandals);
			 */

			/*
			 * List<Map<String, Object>> gps =
			 * directVacancyZptcRepo.gramPanchayatList(districtId, revenueDivisionId);
			 * redirectAttr.addFlashAttribute("gpsNew", gps);
			 * 
			 * List<Map<String, Object>> wardList = gPWiseUpdated.getWardList(districtId,
			 * mandalId, gpcode); redirectAttr.addFlashAttribute("wardListNew", wardList);
			 */
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			
			String userdistrictId = detailsByUserId.getDistrict_id().toString();
			String userrevenueId = detailsByUserId.getRevenueDivisionId().toString(); 
			
			 
			if(userdistrictId!=null && !userdistrictId.isEmpty() && !"0".equals(userdistrictId) )
			{
				if(!districtId.equals(userdistrictId)) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			}
			 
			if(userrevenueId!=null && !userrevenueId.isEmpty() && !"0".equals(userrevenueId))
			{
				if(!userrevenueId.equals(revenueDivisionId) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			} 

			List<Map<String, Object>> vacancyReportData = vacancyReportForGPWardApprovalService
					.findVacancyWardMembers(districtId, revenueDivisionId, assemblyId, mandalId, gpcode, wardId);
			redirectAttr.addFlashAttribute("vacancyReportData", vacancyReportData);
			if (vacancyReportData.size() == 0) {
				redirectAttr.addFlashAttribute("failMessage", "Data Not Found");
			}
			List<Map<String, Object>> wardAbstractReport = vacancyReportForGPWardApprovalService.getWardDetailsForAbstractReport(vacancyReportForGPWardApprovalDTO);
			redirectAttr.addFlashAttribute("wardAbstractReport", wardAbstractReport);
			redirectAttr.addFlashAttribute("districtIdSelect", districtId);
			redirectAttr.addFlashAttribute("revenueDivisionIdSelect", revenueDivisionId);
			redirectAttr.addFlashAttribute("assemblyIdSelect", assemblyId);
			redirectAttr.addFlashAttribute("mandalIdSelect", mandalId);
		//	redirectAttr.addFlashAttribute("gpcodeSelect", gpcode);
			redirectAttr.addFlashAttribute("wardIdSelect", wardId);
		} catch (Exception e) {
            if (e instanceof AccessDeniedException) {
                throw e;
            }
        }
		return "redirect:/getVacancyReportApprovalForGPWard";
	}

	/*@PostMapping("/vacancyGPWardConfirmation")
	public String vacancyGPWardConfirmation(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("assemblyId") String assemblyId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("wardNo") String wardNo, @RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes, HttpServletRequest request) {
		try {
			String insertedBy = (String) request.getSession().getAttribute("loggedInUser");
			Timestamp insertedTime = new Timestamp(System.currentTimeMillis());
			int saveCount = vacancyReportForGPWardApprovalService.saveVacancyGpwardApproval(districtId, mandalId,
					gpcode, revenueDivisionId, wardNo, reasonVacancy, dateOfOccurrence, anyImpediment, remarks,
					insertedBy, request.getRemoteAddr(), insertedTime, assemblyId, vacancyFillDate, "confirmed", id);
			if (saveCount > 0) {
				redirectAttributes.addFlashAttribute("saveSuccess", "Confirmed Successfully..!!");
			} else {
				redirectAttributes.addFlashAttribute("saveFailure", "Failed to Confirm..!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWard";
	}*/
	
	
	
	
	
	@RequestMapping(value = "/vacancyGPWardConfirmation", method = RequestMethod.POST)
	public String vacancyGPWardConfirmation(HttpServletRequest request, 
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,RedirectAttributes redirectAttr) {
		try {
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			String ipAddress = request.getRemoteAddr();
			boolean isSaved = vacancyReportForGPWardApprovalService.saveVacancyGpwardApproval(vacancyReportForGPWardApprovalDTO.getSelectedItems(),vacancyReportForGPWardApprovalDTO, userId , ipAddress);
			if (isSaved) {
				redirectAttr.addFlashAttribute("saveSuccess", "Confirmed Successfully..!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed to Confirm..!!");
			}
		} catch (Exception e) {
			//logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/getVacancyReportApprovalForGPWard";
	}

	/*@PostMapping("/vacancyGPWardRollback")
	public String vacancyGPWardRollback(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("assemblyId") String assemblyId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("wardNo") String wardNo, @RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes, HttpServletRequest request) {
		try {
			String insertedBy = (String) request.getSession().getAttribute("loggedInUser");
			Timestamp insertedTime = new Timestamp(System.currentTimeMillis());
			int saveCount = vacancyReportForGPWardApprovalService.saveVacancyGpwardRollback(districtId, mandalId,
					gpcode, revenueDivisionId, wardNo, reasonVacancy, dateOfOccurrence, anyImpediment, remarks,
					insertedBy, request.getRemoteAddr(), insertedTime, assemblyId, vacancyFillDate, "rollbacked", id);
			if (saveCount > 0) {
				redirectAttributes.addFlashAttribute("saveSuccess", "Rollbacked Successfully..!!");
			} else {
				redirectAttributes.addFlashAttribute("saveFailure", "Failed to Rollback..!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWard";
	}*/
	
	@PostMapping("/vacancyGPWardRollback")
	public String vacancyGPWardRollback(HttpServletRequest request, 
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			String ipAddress = request.getRemoteAddr();
			boolean isSaved = vacancyReportForGPWardApprovalService.saveVacancyGpwardRollback(vacancyReportForGPWardApprovalDTO.getSelectedItems(),vacancyReportForGPWardApprovalDTO, userId , ipAddress);
			if (isSaved) {
				redirectAttr.addFlashAttribute("saveSuccess", "Rollbacked Successfully..!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed to Rollback..!!");
			}
		} catch (Exception e) {
			//logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/getVacancyReportApprovalForGPWard";
	}

	@GetMapping("/getVacancyReportApprovalForGPWardDPO")
	public String getVacancyReportApprovalForGPWardDPO(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			int districtId = Integer.parseInt(detailsByUserId.getDistrict_id());

			String districtName = userRepo.getDistrictNameInt(districtId);
			model.addAttribute("districtName", districtName);

			List<Map<String, Object>> revenueList = userRepo.findRevenueDivisionBasedOnDistrictInt(districtId);
			model.addAttribute("revenueList", revenueList);

			model.addAttribute("districtId", detailsByUserId.getDistrict_id());
			model.addAttribute("admin", "admin");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyReportApprovalForGPWardForDPO";
	}

	@PostMapping("/getVacancyReportDataOfGPWardForDPO")
	public String getVacancyReportDataOfGPWardForDPO(
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			/*String districtId = vacancyReportForGPWardApprovalDTO.getDistrictId();
			String revenueDivisionId = vacancyReportForGPWardApprovalDTO.getRevenueDivisionId();
			String assemblyId = vacancyReportForGPWardApprovalDTO.getAssemblyId();
			String mandalId = vacancyReportForGPWardApprovalDTO.getMandalId();
			String gpcode = vacancyReportForGPWardApprovalDTO.getGpcode();
			String wardId = vacancyReportForGPWardApprovalDTO.getWardId();

			List<Map<String, Object>> assemblyConstituencyList = userRepo.assemblyList(districtId);
			redirectAttr.addFlashAttribute("assemblyConstituencyListNew", assemblyConstituencyList);

			List<Map<String, Object>> mandals = userRepo.findMandalBasedOnRevenueDivision(districtId,
					revenueDivisionId);
			redirectAttr.addFlashAttribute("mandalsNew", mandals);

			List<Map<String, Object>> gps = directVacancyZptcRepo.gramPanchayatList(districtId, revenueDivisionId);
			redirectAttr.addFlashAttribute("gpsNew", gps);

			List<Map<String, Object>> wardList = gPWiseUpdated.getWardList(districtId, mandalId, gpcode);
			redirectAttr.addFlashAttribute("wardListNew", wardList);*/

			List<Map<String, Object>> vacancyReportDataDPO = vacancyReportForGPWardApprovalService
					.findVacancyDetailsDPO(vacancyReportForGPWardApprovalDTO);
			redirectAttr.addFlashAttribute("vacancyReportData", vacancyReportDataDPO);
			
			if (vacancyReportDataDPO.size() == 0) {
				redirectAttr.addFlashAttribute("failMessage", "Data Not Found");
			}
 
			List<Map<String, Object>> listOfMandals =new  ArrayList<>();
			if ("all".equalsIgnoreCase(vacancyReportForGPWardApprovalDTO.getRevenueDivisionIdNew())) {
		        // Fetch all mandals for the district
				listOfMandals = addMandalRepository.getAllMandalsByDistrict(Integer.parseInt(vacancyReportForGPWardApprovalDTO.getDistrictIdNew()));
		    } else {
		    	
		   
			 listOfMandals = addMandalRepository
					.getMandalsByDistRevenueInt(Integer.parseInt(vacancyReportForGPWardApprovalDTO.getDistrictIdNew()), Integer.parseInt(vacancyReportForGPWardApprovalDTO.getRevenueDivisionIdNew()));
		    }
			
			redirectAttr.addFlashAttribute("listOfMandals", listOfMandals);
			redirectAttr.addFlashAttribute("mandalIdSelected", vacancyReportForGPWardApprovalDTO.getMandalIdNew());
			redirectAttr.addFlashAttribute("rdIdSelected", vacancyReportForGPWardApprovalDTO.getRevenueDivisionIdNew());
			/*redirectAttr.addFlashAttribute("districtIdSelect", districtId);
			redirectAttr.addFlashAttribute("revenueDivisionIdSelect", revenueDivisionId);
			redirectAttr.addFlashAttribute("assemblyIdSelect", assemblyId);
			redirectAttr.addFlashAttribute("mandalIdSelect", mandalId);
			redirectAttr.addFlashAttribute("gpcodeSelect", gpcode);
			redirectAttr.addFlashAttribute("wardIdSelect", wardId);*/
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWardDPO";
	}

	/*@PostMapping("/vacancyGPWardConfirmationForDPO")
	public String vacancyGPWardConfirmationForDPO(@RequestParam("slno") Integer slno,
			@RequestParam("districtId") String districtId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("assemblyId") String assemblyId, @RequestParam("mandalId") String mandalId,
			@RequestParam("gpcode") String gpcode, @RequestParam("wardNo") String wardNo,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes, HttpServletRequest request) {
		try {
			String insertedBy = (String) request.getSession().getAttribute("loggedInUser");
			String insertedIp = request.getRemoteAddr();
			Timestamp insertedTime = new Timestamp(System.currentTimeMillis());
			int saveCount = vacancyReportForGPWardApprovalService.saveVacancyGpwardApprovalDPO(districtId, mandalId,
					gpcode, revenueDivisionId, wardNo, reasonVacancy, dateOfOccurrence, anyImpediment, remarks,
					insertedBy, insertedIp, insertedTime, assemblyId, vacancyFillDate, "confirmed", slno);
			if (saveCount > 0) {
				redirectAttributes.addFlashAttribute("saveSuccess", "Confirmed Successfully..!!");
			} else {
				redirectAttributes.addFlashAttribute("saveFailure", "Failed to Confirm..!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWardDPO";
	}*/
	
	
	
	@RequestMapping(value = "/vacancyGPWardConfirmationForDPO", method = RequestMethod.POST)
	public String vacancyGPWardConfirmationForDPO(HttpServletRequest request, 
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,RedirectAttributes redirectAttr) {
		try {
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			String ipAddress = request.getRemoteAddr();
			boolean isSaved = vacancyReportForGPWardApprovalService.saveVacancyGpwardApprovalDPO(vacancyReportForGPWardApprovalDTO.getSelectedItems(),vacancyReportForGPWardApprovalDTO, userId , ipAddress);
			if (isSaved) {
				redirectAttr.addFlashAttribute("saveSuccess", "Confirmed Successfully..!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed to Confirm..!!");
			}
		} catch (Exception e) {
			//logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/getVacancyReportApprovalForGPWardDPO";
	}

	/*@PostMapping("/vacancyGPWardRollbackForDPO")
	public String vacancyGPWardRollbackForDPO(@RequestParam("slno") Integer slno,
			@RequestParam("districtId") String districtId, @RequestParam("revenueDivisionId") String revenueDivisionId,
			@RequestParam("assemblyId") String assemblyId, @RequestParam("mandalId") String mandalId,
			@RequestParam("gpcode") String gpcode, @RequestParam("wardNo") String wardNo,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes, HttpServletRequest request) {
		try {
			String insertedBy = (String) request.getSession().getAttribute("loggedInUser");
			String insertedIp = request.getRemoteAddr();
			Timestamp insertedTime = new Timestamp(System.currentTimeMillis());
			int saveCount = vacancyReportForGPWardApprovalService.saveVacancyGpwardRollbackDPO(districtId, mandalId,
					gpcode, revenueDivisionId, wardNo, reasonVacancy, dateOfOccurrence, anyImpediment, remarks,
					insertedBy, insertedIp, insertedTime, assemblyId, vacancyFillDate, "rollbacked", slno);
			if (saveCount > 0) {
				redirectAttributes.addFlashAttribute("saveSuccess", "Rollbacked Successfully..!!");
			} else {
				redirectAttributes.addFlashAttribute("saveFailure", "Failed to Rollback..!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWardDPO";
	}*/
	
	@RequestMapping(value = "/vacancyGPWardRollbackForDPO", method = RequestMethod.POST)
	public String vacancyGPWardRollbackForDPO(HttpServletRequest request, 
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,RedirectAttributes redirectAttr) {
		try {
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			String ipAddress = request.getRemoteAddr();
			boolean isSaved = vacancyReportForGPWardApprovalService.saveVacancyGpwardRollbackDPO(vacancyReportForGPWardApprovalDTO.getSelectedItems(),vacancyReportForGPWardApprovalDTO, userId , ipAddress);
			if (isSaved) {
				redirectAttr.addFlashAttribute("saveSuccess", "Confirmed Successfully..!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed to Confirm..!!");
			}
		} catch (Exception e) {
			//logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/getVacancyReportApprovalForGPWardDPO";
	}

	@GetMapping("/getVacancyReportApprovalForGPWardCPR")
	public String getVacancyReportApprovalForGPWardCPR(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,HttpServletRequest request) {
		try {
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> DistrictsList = addMandalService.findDistricts();
			model.addAttribute("districtData", DistrictsList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyReportApprovalForGPWardForCPR";
	}

	@PostMapping("/getVacancyReportDataOfGPWardForCPR")
	public String getVacancyReportDataOfGPWardForCPR(
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			HttpServletRequest request, RedirectAttributes redirectAttr,
			@RequestParam(name = "districtIdNew", required = false) String districtId,
			@RequestParam(name = "revenueDivisionIdNew", required = false) String revenueDivisionId,
			@RequestParam(name = "mandalIdNew", required = false) String mandalId) {
		try {
			String firstPart = districtId.contains(",") ? districtId.split(",")[0] : districtId;
			String queryDistrictId = "ALL".equals(firstPart) ? "0" : firstPart;

			List<Map<String, Object>> vacancyReportData = vacancyReportForGPWardApprovalService
					.getVacancyApprovalDetailsForCPR(districtId,revenueDivisionId,mandalId);
			redirectAttr.addFlashAttribute("vacancyReportData", vacancyReportData);

			redirectAttr.addFlashAttribute("districtIdSelect", districtId);
			if (vacancyReportData.size() == 0) {
				redirectAttr.addFlashAttribute("getMsg", "true");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWardCPR";
	}

	@PostMapping("/vacancyGPWardConfirmationForCPR")
	public String vacancyGPWardConfirmationForCPR(
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			RedirectAttributes redirectAttr) {
		try {
			List<VacancyGPWardSelectedItemsList> selectedItems = vacancyReportForGPWardApprovalDTO.getSelectedItems();
			boolean isSaved = vacancyReportForGPWardApprovalService.saveVacancyGPWardCPRConfirmation(selectedItems);
			if (isSaved) {
				redirectAttr.addFlashAttribute("saveSuccess", "Vacancy confirmation saved successfully...!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed to save vacancy confirmation..!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getVacancyReportApprovalForGPWardCPR";
	}

	@GetMapping("/getVacancyReportGPWardConfirmedByCPR")
	public String vacancyGPWardRollbackForCPR(
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO,
			Model model,HttpServletRequest request) {
		try {
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> vacancyApprovalDetailsForView = vacancyReportForGPWardApprovalService
					.getVacancyApprovalDetailsForView();
			model.addAttribute("vacancyApprovalDetailsForView", vacancyApprovalDetailsForView);
			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyReportConfirmedByCPR";
	}
	
	
	@PostMapping(value = "/casualVacancyCPRAbstractReport")
	public String casualVacancyCPRAbstractReport(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO
			,HttpServletRequest request) {
		try {
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> casualWardCPRAbstractReport = vacancyReportForGPWardApprovalService.getCasualWardCPRAbstractReport(vacancyReportForGPWardApprovalDTO);
			model.addAttribute("casualWardCPRAbstractReport", casualWardCPRAbstractReport);
			
			model.addAttribute("vacancyReportForGPWardApprovalDTO", vacancyReportForGPWardApprovalDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "casualVacancyCPRAbstractReport";
	}
	
	
	@PostMapping(value = "/getCasualWardWiseRevenue")
	public String getCasualWardWiseRevenue(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO
			,HttpServletRequest request) {
		try {
			

			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> casualWardCPRAbstractRevenue = vacancyReportForGPWardApprovalService.getCasualWardWiseRevenue(vacancyReportForGPWardApprovalDTO.getDistrictId());
			model.addAttribute("casualWardCPRAbstractRevenue", casualWardCPRAbstractRevenue);
			
			model.addAttribute("vacancyReportForGPWardApprovalDTO", vacancyReportForGPWardApprovalDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "casualVacancyCPRAbstractReport";
	}
	
	
	@PostMapping(value = "/getCasualWardWiseMandal")
	public String getCasualWardWiseMandal(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO
			,HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> casualWardCPRAbstractMandal = vacancyReportForGPWardApprovalService.getCasualWardWiseMandal(vacancyReportForGPWardApprovalDTO.getDistrictId(),vacancyReportForGPWardApprovalDTO.getRevenueDivisionId());
			model.addAttribute("casualWardCPRAbstractMandal", casualWardCPRAbstractMandal);
			
			model.addAttribute("vacancyReportForGPWardApprovalDTO", vacancyReportForGPWardApprovalDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "casualVacancyCPRAbstractReport";
	}
	
	
	@PostMapping(value = "/getCasualWardWiseGramPanchayats")
	public String getCasualWardWiseGramPanchayats(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO
			,HttpServletRequest request) {
		try {
			
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> casualWardCPRAbstractGramPanchayats = vacancyReportForGPWardApprovalService.getCasualWardWiseGramPanchayats(vacancyReportForGPWardApprovalDTO.getDistrictId(),
					vacancyReportForGPWardApprovalDTO.getRevenueDivisionId(),vacancyReportForGPWardApprovalDTO.getMandalId());
			model.addAttribute("casualWardCPRAbstractGramPanchayats", casualWardCPRAbstractGramPanchayats);
			
			model.addAttribute("vacancyReportForGPWardApprovalDTO", vacancyReportForGPWardApprovalDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "casualVacancyCPRAbstractReport";
	}
	
	
	@PostMapping(value = "/getCasualWardWiseWard")
	public String getCasualWardWiseWard(Model model,
			@ModelAttribute("vacancyReportForGPWardApprovalDTO") VacancyReportForGPWardApprovalDTO vacancyReportForGPWardApprovalDTO
			,HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> casualWardCPRAbstractWard = vacancyReportForGPWardApprovalService.getCasualWardWiseWard(vacancyReportForGPWardApprovalDTO.getDistrictId(),
					vacancyReportForGPWardApprovalDTO.getRevenueDivisionId(),vacancyReportForGPWardApprovalDTO.getMandalId()
					,vacancyReportForGPWardApprovalDTO.getGpcode());
			model.addAttribute("casualWardCPRAbstractWard", casualWardCPRAbstractWard);
			
			model.addAttribute("vacancyReportForGPWardApprovalDTO", vacancyReportForGPWardApprovalDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "casualVacancyCPRAbstractReport";
	}
}