package cgg.gov.in.apsec.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyForchairpersonEntity;
import cgg.gov.in.apsec.repo.DirectVacancyForZPTCRepository;
import cgg.gov.in.apsec.repo.MasterMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyForChairpersonRepository;

@Controller
public class VacancyForChairpersonController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private MasterMandalRepository mastermandalrepo;

	@Autowired
	private VacancyForChairpersonRepository vacancyRepo;

	@Autowired
	private DirectVacancyForZPTCRepository directVacancyZptcRepo;

	@GetMapping(value = "/vacancyForChairperson")
	public String vacancyForChairperson(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();

			List<Map<String, Object>> revenue = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
			model.addAttribute("revenue", revenue);

			model.addAttribute("districtId", districtId);
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForChairperson";
	}

	@PostMapping(value = "/vacancyForChairpersonSave")
	public String vacancyForChairpersonSave(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			vacancyForchairpersonEntity.setUpdatedBy(userId);

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForchairpersonEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;
			if (vacancyForchairpersonEntity.getVacancyFillDatee() != null
					&& !vacancyForchairpersonEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForchairpersonEntity.getVacancyFillDatee());
				vacancyForchairpersonEntity.setVacancyFillDate(dateOfFilling);
			}

			vacancyForchairpersonEntity.setDateOfOccurrence(parsedDate);
			vacancyForchairpersonEntity.setVacancyFillDate(dateOfFilling);

			vacancyRepo.save(vacancyForchairpersonEntity);
			redirectAttr.addFlashAttribute("msg", "Vacancy for Chairperson Added Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Add Chairperson ! Please try again....");
		}
		return "redirect:/vacancyForChairperson";
	}

	@GetMapping(value = "/getVacancyForChairperson")
	public String getVacancyForChairperson(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			List<Map<String, Object>> VacancyForChairperson = vacancyRepo.getVacancyDetailsByDistrictAndGpcode(userId);
			model.addAttribute("VacancyForChairperson", VacancyForChairperson);

			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForChairpersonList";
	}

	@GetMapping(value = "/vacancyForChairpersonUpdateForm")
	public String vacancyForChairpersonUpdateForm(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			Date utilDate3 = null;
			String dateText3 = null;

			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> VacancyForChairpersonUpdate = vacancyRepo.getVacancyUpdateForm(id);
			if (!VacancyForChairpersonUpdate.isEmpty()) {
				Map<String, Object> firstRow = VacancyForChairpersonUpdate.get(0);

				String districtid = (String) firstRow.get("district_id");
				vacancyForchairpersonEntity.setDistrictId(districtid);
				model.addAttribute("district_id", districtid);

				String rid = (String) firstRow.get("revenue_division_id");
				vacancyForchairpersonEntity.setRevenueDivisionId(rid);
				model.addAttribute("revenue_division_id", rid);

				String revenueName = userRepo.getRevenueDivisionName(rid, districtid);
				model.addAttribute("revenueName", revenueName);

				String gpcode = (String) firstRow.get("gpcode");
				vacancyForchairpersonEntity.setGpcode(gpcode);
				model.addAttribute("gpcode", gpcode);

				String gpName = directVacancyZptcRepo.getGPName(districtid, rid, gpcode);
				model.addAttribute("gpName", gpName);

				String officeName = (String) firstRow.get("office_name");
				vacancyForchairpersonEntity.setOfficeName(officeName);
				model.addAttribute("office_name", officeName);

				String reason = (String) firstRow.get("reason_vacancy");
				vacancyForchairpersonEntity.setReasonVacancy(reason);
				model.addAttribute("reason_vacancy", reason);

				String any = (String) firstRow.get("any_impediment");
				vacancyForchairpersonEntity.setAnyImpediment(any);
				model.addAttribute("any_impediment", any);

				String remarks = (String) firstRow.get("remarks");
				vacancyForchairpersonEntity.setRemarks(remarks);
				model.addAttribute("remarks", remarks);

				String any1 = (String) firstRow.get("any_vacancy");
				vacancyForchairpersonEntity.setAnyVacancy(any1);
				model.addAttribute("any_vacancy", any1);

				String electedName = (String) firstRow.get("elected_representative_name");
				vacancyForchairpersonEntity.setElectedRep(electedName);
				model.addAttribute("elected_representative_name", electedName);

				String remarks1 = (String) firstRow.get("territorial_constituency");
				vacancyForchairpersonEntity.setTerritorialConstituency(remarks1);
				model.addAttribute("territorial_constituency", remarks1);

				java.sql.Date sqlDate = (java.sql.Date) firstRow.get("date_of_occurrence");
				java.sql.Date sqlDate3 = (java.sql.Date) firstRow.get("vacancy_fill_date");

				Date utilDate = new Date(sqlDate.getTime());

				SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
				String dateText = dateFormat.format(utilDate);

				if (sqlDate3 != null) {
					utilDate3 = new Date(sqlDate3.getTime());
					dateText3 = dateFormat.format(utilDate3);
				}

				vacancyForchairpersonEntity.setDateOfOccurrence(utilDate);
				vacancyForchairpersonEntity.setVacancyFillDate(utilDate3);

				model.addAttribute("date_of_occurrence", dateText);
				model.addAttribute("vacancy_fill_date", dateText3);
				List<Map<String, Object>> zppList = userRepo.getZPTCNamesList(districtid, rid, gpcode);
				model.addAttribute("zppList", zppList);

				model.addAttribute("firstRow", firstRow);
			}
			model.addAttribute("populate", "populate");
			model.addAttribute("id", id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForChairperson";
	}

	@Transactional
	@PostMapping(value = "/UpdateVacancyDirectForChairperson")
	public String UpdateVacancyDirectForChairperson(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			String districtId = vacancyForchairpersonEntity.getDistrictId();
			String gpcode = vacancyForchairpersonEntity.getGpcode();
			String rdId = vacancyForchairpersonEntity.getRevenueDivisionId();
			String officeName = vacancyForchairpersonEntity.getOfficeName();
			String reason = vacancyForchairpersonEntity.getReasonVacancy();
			String any = vacancyForchairpersonEntity.getAnyImpediment();
			String remarks = vacancyForchairpersonEntity.getRemarks();
			String eleRep = vacancyForchairpersonEntity.getElectedRep();

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForchairpersonEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;
			if (vacancyForchairpersonEntity.getVacancyFillDatee() != null
					&& !vacancyForchairpersonEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForchairpersonEntity.getVacancyFillDatee());
			}

			vacancyForchairpersonEntity.setDateOfOccurrence(parsedDate);
			vacancyForchairpersonEntity.setVacancyFillDate(dateOfFilling);
			if (dateOfFilling != null) {
				vacancyRepo.updateVacancyForChairperson(districtId, gpcode, officeName, rdId, reason, parsedDate, any,
						dateOfFilling, remarks, vacancyForchairpersonEntity.getAnyVacancy(),
						vacancyForchairpersonEntity.getTerritorialConstituency(), userId, eleRep, id);
			} else {
				vacancyRepo.updateVacancyForChairpersonWithoutFilldate(districtId, gpcode, officeName, rdId, reason,
						parsedDate, any, remarks, vacancyForchairpersonEntity.getAnyVacancy(),
						vacancyForchairpersonEntity.getTerritorialConstituency(), userId, eleRep, id);
			}

			redirectAttr.addFlashAttribute("msg", "Vacancy for Chairperson Updated Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Update  ! Please try again....");
		}
		return "redirect:/getVacancyForChairperson";
	}

	@Transactional
	@GetMapping(value = "/deleteVacancyForChairperson")
	public String deleteVacancyForChairperson(RedirectAttributes redirectAttr,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			vacancyRepo.deleteByIdNative(id);
			redirectAttr.addFlashAttribute("msg", "Vacancy for Chairperson Deleted Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Delete  ! Please try again....");
		}
		return "redirect:/getVacancyForChairperson";
	}
}