package cgg.gov.in.apsec.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.ReservationForZptcRoDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.ElectionProcessUrbanLocalBodiesService;
import cgg.gov.in.apsec.service.PostalBallotService;
import cgg.gov.in.apsec.service.ReservationForZptcROService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class ZptcErPrintController {

	@Autowired
	private ReservationForZptcROService reservationForZptcROService;
	
	@Autowired
	private UserRepository userRepo;
	
	@Autowired
	PostalBallotService postalBallotService;
	
	@Autowired
	private ElectionProcessUrbanLocalBodiesService urbanService;
	
	@GetMapping("/getErPrintForZptc")
	public String getFinalListOfContestingCandidatesForZptc(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO reservationForZptcRoDTO,
			Model model, HttpServletRequest request) {
		
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			int roleId = user.getRole_id();
			if(roleId == 44 || roleId == 1) {
				List<Map<String,Object>> districtList = userRepo.findDistricts();
				model.addAttribute("districtList", districtList);
			}
			else {
				String districtId = user.getDistrict_id();
				String districtName = userRepo.getDistrictNameInt(Integer.parseInt(districtId));
				model.addAttribute("districtName", districtName);
				model.addAttribute("districtId", districtId);
				List<Map<String, Object>> rdDataForRoZpp = userRepo.findRevenueDivisionBasedOnDistrictInt(Integer.parseInt(districtId));
				model.addAttribute("rdDataForRoZpp", rdDataForRoZpp);
			}
			
			
			List<Map<String,Object>> data = userRepo.getElectoralRollNotificationDataMptc();
			model.addAttribute("electionDesc", data);
			
			List<Map<String, Object>> electionDuties = postalBallotService.getElectionDuties();
			model.addAttribute("electionDuties", electionDuties);
			
		}
		catch(Exception e) {
			
			e.printStackTrace();
		}
		return "erPrintForZptc";
	}
	
	@GetMapping("/getZPTCDataForZptcErPrint")
	@ResponseBody
	public String getZPTCDataForZptcErPrint(@RequestParam("districtId") String districtId,@RequestParam("rdId") String rdId) {
		
	 
		StringBuilder mainData = new StringBuilder();
		try {
			
			List<Map<String,Object>>  zptcData = userRepo.getMandaList(Integer.parseInt(districtId), Integer.parseInt(rdId));
			mainData.append("<option value='0'> -- Select -- </option>");
			for (Map<String, Object> map : zptcData) {
		    	
					mainData.append("<option value='" + map.get("mandal_id") + "'>" + map.get("mandal_name") + "</option>");
			}
		}
		catch(Exception e) {
			
			e.printStackTrace();
		}
		
		return mainData.toString();
	}
	
	@PostMapping("/getSummaryDataBasedOnZptc")
	public String getSummaryDataBasedOnZptc(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO dto,
			RedirectAttributes rdAttr, HttpServletRequest request) {
		
		try {
			
			List<Map<String, Object>> gpWiseDataForZptcErPrint = reservationForZptcROService.getGPWiseDataForZptcErPrint(dto.getDistrictId(), dto.getRdId(), dto.getZptcId());
			rdAttr.addFlashAttribute("gpWiseDataForZptcErPrint", gpWiseDataForZptcErPrint);
			rdAttr.addFlashAttribute("rdAttrData", "rdAttrData");			
			rdAttr.addFlashAttribute("rdIdSelected", dto.getRdId());
			rdAttr.addFlashAttribute("electionIdSelected", dto.getElectionCode());
			List<Map<String,Object>>  zptcData = userRepo.getMandaList(Integer.parseInt(dto.getDistrictId()), Integer.parseInt(dto.getRdId()));
			rdAttr.addFlashAttribute("zptcDataRedirect", zptcData);
			rdAttr.addFlashAttribute("zptcIdSelected", dto.getZptcId());
			String zptcName = reservationForZptcROService.getZptcName(Integer.parseInt(dto.getDistrictId()), Integer.parseInt(dto.getRdId()), Integer.parseInt(dto.getZptcId()));
			rdAttr.addFlashAttribute("zptcName", zptcName);
			
			rdAttr.addFlashAttribute("districtIdSelected",dto.getDistrictId());
			rdAttr.addFlashAttribute("rdIdSelected",dto.getRdId());
			rdAttr.addFlashAttribute("rdDataForRoZpp",userRepo.findRevenueDivisionBasedOnDistrict(dto.getDistrictId()));
		}
		catch(Exception e) {
			
			e.printStackTrace();
		}
		
		return "redirect:/getErPrintForZptc";
	}
	
	@GetMapping("/getZptcMukhapatram")
	public String getMptcErPrint(@RequestParam("election_id") String electionId,@RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("zptcId") String zptcId,Model model,HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String userdistrictId = detailsByUserId.getDistrict_id().toString();
			if(userdistrictId!=null && !("0").equals(userdistrictId) && !userdistrictId.isEmpty()) {
				if(!userdistrictId.equals(districtId)) {
	                throw new AccessDeniedException("You are not authorized to view this data.");
	            }
			}
			String districtName = userRepo.getDistrictNameTelInt(Integer.parseInt(districtId));
			String revenueDivisionName = userRepo.getRevenueDivisionNameTelInt(Integer.parseInt(revenueDivisionId),
					Integer.parseInt(districtId));
			 
			String zptcName = reservationForZptcROService.findZptcNameTeluguByIds(Integer.parseInt(districtId), Integer.parseInt(revenueDivisionId), Integer.parseInt(zptcId));
			
			
			Map<String, Object> zppData = reservationForZptcROService.findZppDetails(Integer.parseInt(districtId), Integer.parseInt(revenueDivisionId), Integer.parseInt(zptcId));
			 
			String zppName = zppData.get("zpp_name_telugu").toString();
			 
			model.addAttribute("districtName", districtName);
			model.addAttribute("revenueDivisionName", revenueDivisionName);
			model.addAttribute("zppName", zppName);
			model.addAttribute("zptcName", zptcName);
			model.addAttribute("mppName", zptcName);
			
			List<Map<String, Object>> acByDisRevMan = userRepo.getACByDisRevMan(districtId, revenueDivisionId, zptcId);
			if(acByDisRevMan.size()==1) {
				model.addAttribute("assemblyUniqName",userRepo.getAssemblyConstituencyTeluguName(districtId,
						revenueDivisionId, acByDisRevMan.get(0).get("assembly_id").toString()));
			}
			else {
				String uniqueName="";
				for (Map<String, Object> map : acByDisRevMan) {
					uniqueName=uniqueName+","+userRepo.getAssemblyConstituencyTeluguName(districtId,
							revenueDivisionId, map.get("assembly_id").toString());
				}
				model.addAttribute("assemblyUniqNames",uniqueName);
			}
			
			List<Map<String, Object>> gpWiseDataForZptcErPrint = reservationForZptcROService.getGPWiseDataForZptcErPrint(districtId, revenueDivisionId, zptcId);
			model.addAttribute("gpWiseDataForZptcErPrint", gpWiseDataForZptcErPrint);
			Integer gpsCount=gpWiseDataForZptcErPrint.size();
			model.addAttribute("gpsCount", gpsCount);
			
			Map<String, Object> fromAndToPartNos = reservationForZptcROService.findMinAndMaxPartNumbersForZptcMukhapatram(Integer.parseInt(districtId), Integer.parseInt(revenueDivisionId), Integer.parseInt(zptcId));
			model.addAttribute("fromAndToPartNos", fromAndToPartNos);
			String electionYearNew= urbanService.findElectionYearById(Integer.parseInt(electionId));
			model.addAttribute("electionYearNew", electionYearNew);
			
			List<Map<String,Object>> electionData = userRepo.getElectoralRollNotificationDataMptc();
			if (electionData != null && electionId != null) {
			    Optional<Map<String, Object>> gpRecordOptional = electionData.stream()
			        .filter(record -> {
			            Object id = record.get("id");
			            return id != null && id.toString().equals(electionId);  
			        })
			        .findFirst();  

			    if (gpRecordOptional.isPresent()) {
			        Map<String, Object> gpRecord = gpRecordOptional.get();
			        Object gpDate = gpRecord.get("date_of_publications_of_gp_roll");
			        Object electionDescription = gpRecord.get("election_description");

			        model.addAttribute("gpDate", gpDate != null ? gpDate : "");
			        model.addAttribute("electionDescription", electionDescription != null ? electionDescription : "");

			        System.out.println("Date of Publications of GP Roll: " + gpDate);
			        System.out.println("Election Description: " + electionDescription);
			    } else {
			        System.out.println("No matching record found for electionId: " + electionId);
			        model.addAttribute("gpDate", "");
			        model.addAttribute("electionDescription", "");
			    }
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return "zptcMukhapatramPage";
	}
	
	@GetMapping("/getErPrintForZptcRo")
	public String getErPrintForZptcRo(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO reservationForZptcRoDTO,
			Model model, HttpServletRequest request) {
		
		try {
			
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String zppCode = user.getZppCode();
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			Map<String, Object> zppData = reservationForZptcROService.getZppForROLogin(zppCode);
			model.addAttribute("zppData", zppData);

			List<Integer> districtIds = reservationForZptcROService.findDistrictIdByZppCode(Integer.parseInt(zppCode));

			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(userId);
			model.addAttribute("electionsList", elections);

			List<Map<String, Object>> districtList = reservationForZptcROService.findDistrictsByIds(districtIds);
			model.addAttribute("districtList", districtList);

			
			List<Map<String,Object>> data = userRepo.getElectoralRollNotificationDataMptc();
			model.addAttribute("electionDesc", data);
			
			List<Map<String, Object>> electionDuties = postalBallotService.getElectionDuties();
			model.addAttribute("electionDuties", electionDuties);
			
		}
		catch(Exception e) {
			
			e.printStackTrace();
		}
		return "erPrintForZptcRo";
	}
	@PostMapping("/getSummaryDataBasedOnZptcRo")
	public String getSummaryDataBasedOnZptcRo(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO dto,
			RedirectAttributes rdAttr, HttpServletRequest request,Model model) {
		
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			List<Map<String, Object>> gpWiseDataForZptcErPrint = reservationForZptcROService.getGPWiseDataForZptcErPrint(dto.getDistrictId(), dto.getRdId(), dto.getZptcId());
			model.addAttribute("gpWiseDataForZptcErPrint", gpWiseDataForZptcErPrint);
			model.addAttribute("rdAttrData", "rdAttrData");			
			model.addAttribute("rdIdSelected", dto.getRdId());
			model.addAttribute("electionIdSelected", dto.getElectionCode());
			List<Map<String,Object>>  zptcData = userRepo.getMandaList(Integer.parseInt(dto.getDistrictId()), Integer.parseInt(dto.getRdId()));
			model.addAttribute("zptcDataRedirect", zptcData);
			model.addAttribute("zptcIdSelected", dto.getZptcId());
			String zptcName = reservationForZptcROService.getZptcName(Integer.parseInt(dto.getDistrictId()), Integer.parseInt(dto.getRdId()), Integer.parseInt(dto.getZptcId()));
			model.addAttribute("zptcName", zptcName);
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String zppCode = user.getZppCode();

			Map<String, Object> zppData = reservationForZptcROService.getZppForROLogin(zppCode);
			model.addAttribute("zppData", zppData);

			List<Integer> districtIds = reservationForZptcROService.findDistrictIdByZppCode(Integer.parseInt(zppCode));

			List<Map<String, Object>> elections = userRepo.getElectionsForFormZptc(userId);
			model.addAttribute("electionsList", elections);

			List<Map<String, Object>> districtList = reservationForZptcROService.findDistrictsByIds(districtIds);
			model.addAttribute("districtList", districtList);

			
			List<Map<String,Object>> data = userRepo.getElectoralRollNotificationDataMptc();
			model.addAttribute("electionDesc", data);
			
			List<Map<String, Object>> electionDuties = postalBallotService.getElectionDuties();
			model.addAttribute("electionDuties", electionDuties);
		}
		catch(Exception e) {
			
			e.printStackTrace();
		}
		
		return "erPrintForZptcRo";
	}
}
