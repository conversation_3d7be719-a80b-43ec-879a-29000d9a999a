package cgg.gov.in.apsec.asdvoter.service;

import java.util.List;
import java.util.Map;

import cgg.gov.in.apsec.pojo.ElectionProcessRuralLocalGovtDTO;

public interface MptcMarkedCopyService {

	List<Map<String, Object>> getMPTCWiseData(ElectionProcessRuralLocalGovtDTO electionProcessRuralLocalGovtDTO);

	Map<String,Object> getMukhapatramData(String districtId, String revenueDivisionId, String mppCode, String mptcId, String psCode);

	Map<String, Object> findPollingStationDetails(String districtId, String revenueDivisionId, String mandalId,
			String mptcId, String gpcode, String psNo);

}
