package cgg.gov.in.apsec.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.asdvoter.repo.UNContestedElectionResultsforWardMemsReportsRepository;
import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyForMunicipalCorporationsWardMembersDTO;
import cgg.gov.in.apsec.modal.VacancyForMunicipalEntity;

import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.ULBOrdinaryRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyForMunicipalCorporationsWardMembersRepo;
import cgg.gov.in.apsec.repo.VacancyForNagarpanchayatsRepository;
import cgg.gov.in.apsec.service.AddGPWardService;
import cgg.gov.in.apsec.service.UrbanRITBPOTPSScrutinyService;
import cgg.gov.in.apsec.service.VacancyForMunicipalCorporationsWardMembersService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyForMunicipalCorporationsWardMembersController {
	
	@Autowired
	private UserRepository userRepo;

	@Autowired
	private DistrictRepo districtRepo;
	
	@Autowired
	UrbanRITBPOTPSScrutinyService urbanRITBPOTPSScrutinyService;
	
	@Autowired
	private AddGPWardService gpWardSevice;
	
	@Autowired
	VacancyForMunicipalCorporationsWardMembersService vacancyForMunicipalCorporationsWardMembersService;
	
	
	@Autowired
	UNContestedElectionResultsforWardMemsReportsRepository unContestedElectionResultsforWardMemsReportsRepository;
	
	@Autowired
	VacancyForMunicipalCorporationsWardMembersRepo vacancyForMunicipalCorporationsWardMembersRepo;
	
	@Autowired
	private VacancyForNagarpanchayatsRepository vacancyRep;
	@Autowired
	private ULBOrdinaryRepository ulbOrdinaryRepo;
	
	@GetMapping(value = "/vacancyForMunicipalCorporationsWardMembers")
	public String vacancyForMunicipalCorporationsWardMembers(Model model,
			@ModelAttribute("VacancyForMunicipalCorporationsWardMembersDTO") VacancyForMunicipalCorporationsWardMembersDTO vacancyForMunicipalCorporationsWardMembersDTO,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			List<Map<String, Object>> data = new ArrayList<>();

			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);

//				List<Map<String, Object>> assemblyList = userRepo.assemblyList(districtId);
				List<Map<String, Object>> assembliyList = ulbOrdinaryRepo.getAssemblyListByULBCode(detailsByUserId.getUlgCode(),
						detailsByUserId.getDistrict_id());
				model.addAttribute("assemblyList", assembliyList);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
			
			
			String ulgType = userRepo.getUlgTypeForVacancies(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode());
				Map<String, Object> ulgData = userRepo.getUlgNoOfWardsForVacancies(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(), ulgType);
				model.addAttribute("ulbName", ulgData.get("ulb_name"));
				//model.addAttribute("noOfWards", ulgData.get("no_of_wards"));
				model.addAttribute("ulbType", ulgType);
				model.addAttribute("ulgCode", ulgData.get("ulb_code"));	
				
			if (detailsByUserId.getWardId() == null || detailsByUserId.getWardId().equals("")) {
				model.addAttribute("wardsList",
						userRepo.getUlgWards(districtId, ulgType, detailsByUserId.getUlgCode()));
			}
		
			model.addAttribute("vacancyForMunicipalCorporationsWardMembersDTO", vacancyForMunicipalCorporationsWardMembersDTO);
			
		} catch (Exception e) {
			e.printStackTrace();
		}	
		
		return "vacancyForMunicipalCorporationsWardMembers";
	}
	
	@RequestMapping(value = "/getNoOfWardsForULB", method = RequestMethod.GET)
	public @ResponseBody String getNoOfWardsForULB(
			@RequestParam(name = "district_id", required = false) String district_id,
			@RequestParam(name = "ulbType", required = false) String ulbType,
			@RequestParam(name = "ulgCode", required = false) String ulgCode) {
		StringBuilder mainData = new StringBuilder();
		try {
			List<Map<String, Object>> wardList = unContestedElectionResultsforWardMemsReportsRepository.getNoOfWardsForULB(district_id, ulbType, ulgCode);
			for (Map<String, Object> map : wardList) {
				mainData.append(
						"<option value='" + map.get("no_of_wards") + "'>"  + map.get("no_of_wards") + "</option>");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return mainData.toString();
	}
	
	
	
	@PostMapping(value = "/vacancyForMunicipalCorporationsWardMembersSave")
	public String vacancyForMunicipalCorporationsWardMembersSave(Model model,
			@ModelAttribute("VacancyForMunicipalCorporationsWardMembersDTO") VacancyForMunicipalCorporationsWardMembersDTO vacancyForMunicipalCorporationsWardMembersDTO,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");		 		 		
			vacancyForMunicipalCorporationsWardMembersDTO.setUpdatedBy(userId);
			vacancyForMunicipalCorporationsWardMembersDTO.setSubmittedByUser(userId);

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForMunicipalCorporationsWardMembersDTO.getDateOfOccurrencee());
			Date dateOfFilling = null;
			
			Date reportOfvacancy = dateFormat.parse(vacancyForMunicipalCorporationsWardMembersDTO.getReportingOfVacancyStr());
			if(reportOfvacancy != null)
			{
				vacancyForMunicipalCorporationsWardMembersDTO.setReportingOfVacancy(reportOfvacancy);
			}

			if (vacancyForMunicipalCorporationsWardMembersDTO.getVacancyFillDatee() != null
					&& !vacancyForMunicipalCorporationsWardMembersDTO.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForMunicipalCorporationsWardMembersDTO.getVacancyFillDatee());
			}

			vacancyForMunicipalCorporationsWardMembersDTO.setDateOfOccurrence(parsedDate);
			if (dateOfFilling != null) {
				vacancyForMunicipalCorporationsWardMembersDTO.setVacancyFillDate(dateOfFilling);
			}
			
			Boolean checkIfExists = vacancyForMunicipalCorporationsWardMembersRepo.existsByDistrictIdAndUlgCodeAndWardIdAndDateOfOccurrence(vacancyForMunicipalCorporationsWardMembersDTO.getDistrictId(),
					vacancyForMunicipalCorporationsWardMembersDTO.getUlgCode(), vacancyForMunicipalCorporationsWardMembersDTO.getWardId(),
					parsedDate);
			if(!checkIfExists) {
			vacancyForMunicipalCorporationsWardMembersRepo.save(vacancyForMunicipalCorporationsWardMembersDTO);
			redirectAttr.addFlashAttribute("msg", "Vacancy Added Successfully....");
		}
		else {
			redirectAttr.addFlashAttribute("msgfail", "Vacancy for Ward - "+vacancyForMunicipalCorporationsWardMembersDTO.getWardId()+" already exists for this date of occurrence ");
		} 
		}
		catch (Exception e) {				
			e.printStackTrace();			 

			if (e instanceof AccessDeniedException) {
				throw (AccessDeniedException) e;
			}
			redirectAttr.addFlashAttribute("msgfail", "Failed to Add! Please try again....");
		}

		return "redirect:/vacancyForMunicipalCorporationsWardMembers";
	}
	
	
	@GetMapping(value = "/getVacancyForMunicipalCorporationsWardMembers")
	public String getVacancyForMunicipalCorporationsWardMembers(Model model,
			@ModelAttribute("VacancyForMunicipalCorporationsWardMembersDTO") VacancyForMunicipalCorporationsWardMembersDTO vacancyForMunicipalCorporationsWardMembersDTO,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			List<Map<String, Object>> VacancyForMunicipalWardMembers = vacancyForMunicipalCorporationsWardMembersRepo.getVacancyForMunicipalCorporationsWardMembers(userId);
			model.addAttribute("VacancyForMunicipalWardMembers", VacancyForMunicipalWardMembers);

			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMunicipalCorporationsWardMembersList";
	}
	
	
	@PostMapping(value = "/vacancyForMunicipalCorporationsWardUpdateForm")
	public String vacancyForMunicipalCorporationsWardUpdateForm(Model model,HttpServletRequest request,
			@ModelAttribute("VacancyForMunicipalCorporationsWardMembersDTO") VacancyForMunicipalCorporationsWardMembersDTO 
			vacancyForMunicipalCorporationsWardMembersDTO	) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			int id = vacancyForMunicipalCorporationsWardMembersDTO.getId();
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);
			
//			List<Map<String, Object>> assemblyList = userRepo.assemblyList(detailsByUserId.getDistrict_id());
			List<Map<String, Object>> assembliyList = ulbOrdinaryRepo.getAssemblyListByULBCode(detailsByUserId.getUlgCode(),
					detailsByUserId.getDistrict_id());
			model.addAttribute("assemblyList", assembliyList);
			

			List<Map<String, Object>> VacancyForMunicipalWardUpdate = vacancyForMunicipalCorporationsWardMembersRepo.getVacancyUpdateFormWard(id);
			if (!VacancyForMunicipalWardUpdate.isEmpty()) {
				Map<String, Object> firstRow = VacancyForMunicipalWardUpdate.get(0);
				String districtid = (String) firstRow.get("district_id");
				vacancyForMunicipalCorporationsWardMembersDTO.setDistrictId(districtid);
				model.addAttribute("districtId", districtid);

				String distName = userRepo.getDistrictName(districtid);
				model.addAttribute("districtName", distName);

				String assemblyId = (String) firstRow.get("assembly_id");
				vacancyForMunicipalCorporationsWardMembersDTO.setAssemblyId(assemblyId);
				model.addAttribute("assembly_id", assemblyId);

				String assemblyName = vacancyRep.getAssemblyConstituencyName(districtid, assemblyId);
				model.addAttribute("assemblyName", assemblyName);

				String wardNo = (String) firstRow.get("no_of_wards");
				vacancyForMunicipalCorporationsWardMembersDTO.setWardId(wardNo);
				model.addAttribute("wardId", wardNo);

				String name = (String) firstRow.get("ulb_name");
				vacancyForMunicipalCorporationsWardMembersDTO.setUlbName(name);
				model.addAttribute("ulb_name", name);

				String reason = (String) firstRow.get("reason_vacancy");
				vacancyForMunicipalCorporationsWardMembersDTO.setReasonVacancy(reason);
				model.addAttribute("reason_vacancy", reason);

				String remarks = (String) firstRow.get("remarks");
				vacancyForMunicipalCorporationsWardMembersDTO.setRemarks(remarks);
				model.addAttribute("remarks", remarks);

				String anyImpediment = (String) firstRow.get("any_impediment");
				vacancyForMunicipalCorporationsWardMembersDTO.setAnyImpediment(anyImpediment);
				model.addAttribute("any_impediment", anyImpediment);
				
				String ulbType = (String) firstRow.get("ulb_type");
				vacancyForMunicipalCorporationsWardMembersDTO.setUlbType(ulbType);
				model.addAttribute("ulbType", ulbType);
				
				String ulbCode = (String) firstRow.get("ulb_code");
				vacancyForMunicipalCorporationsWardMembersDTO.setUlgCode(ulbCode);
				model.addAttribute("ulgCode", ulbCode);
				
				String electedName = (String) firstRow.get("elected_representative_name");
				vacancyForMunicipalCorporationsWardMembersDTO.setElectedRep(electedName);
				model.addAttribute("elected_representative_name", electedName);

				java.sql.Date sqlDate = (java.sql.Date) firstRow.get("date_of_occurrence");
				java.sql.Date sqlDate3 = (java.sql.Date) firstRow.get("vacancy_fill_date");

				Date utilDate = new Date(sqlDate.getTime());
				Date utilDate3 = null;
				if (sqlDate3 != null && !sqlDate3.toString().trim().isEmpty()) {
					utilDate3 = new Date(sqlDate3.getTime());
				}

				SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				String dateText = dateFormat.format(utilDate);
				String dateText3 = null;
				if (utilDate3 != null) {
					dateText3 = dateFormat.format(utilDate3);
				}

				vacancyForMunicipalCorporationsWardMembersDTO.setDateOfOccurrence(utilDate);
				if (utilDate3 != null) {
					vacancyForMunicipalCorporationsWardMembersDTO.setVacancyFillDate(utilDate3);
				}
				
				model.addAttribute("date_of_occurrence", dateText);
				if (dateText3 != null) {
					model.addAttribute("vacancy_fill_date", dateText3);
				}
				
				
				java.sql.Date sqlDate4 = (java.sql.Date) firstRow.get("reporting_of_vacancy");
				Date utilDate4 = new Date(sqlDate4.getTime());
				String dateText4 = dateFormat.format(utilDate4);
				model.addAttribute("reporting_of_vacancy", dateText4);
				
				
				
				model.addAttribute("firstRow", firstRow);
				
				Integer wardList = userRepo.getUlgWards(districtid, ulbType, ulbCode);
				model.addAttribute("wardsList", wardList);
			}
			
			
			model.addAttribute("populate", "populate");
			model.addAttribute("id", id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMunicipalCorporationsWardMembers";
	}
	
	
	
	@Transactional
	@PostMapping(value = "/UpdateVacancyForMunicipalCorporationsWardMembers")
	public String UpdateVacancyForMunicipalCorporationsWardMembers(RedirectAttributes redirectAttr,
			@ModelAttribute("VacancyForMunicipalCorporationsWardMembersDTO") VacancyForMunicipalCorporationsWardMembersDTO vacancyForMunicipalCorporationsWardMembersDTO,
			HttpServletRequest request, @RequestParam(name = "id", required = false) Integer id) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			String districtId = vacancyForMunicipalCorporationsWardMembersDTO.getDistrictId();
			String officeName = vacancyForMunicipalCorporationsWardMembersDTO.getOfficeName();
			String assemblyId = vacancyForMunicipalCorporationsWardMembersDTO.getAssemblyId();
			String reason = vacancyForMunicipalCorporationsWardMembersDTO.getReasonVacancy();
			String remarks = vacancyForMunicipalCorporationsWardMembersDTO.getRemarks();
			String eleRep = vacancyForMunicipalCorporationsWardMembersDTO.getElectedRep();
			 			 			 
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForMunicipalCorporationsWardMembersDTO.getDateOfOccurrencee());
			Date parsedDateReporting = dateFormat.parse(vacancyForMunicipalCorporationsWardMembersDTO.getReportingOfVacancyStr());
			Date dateOfFilling = null;

			if (vacancyForMunicipalCorporationsWardMembersDTO.getVacancyFillDatee() != null
					&& !vacancyForMunicipalCorporationsWardMembersDTO.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForMunicipalCorporationsWardMembersDTO.getVacancyFillDatee());
			}
			vacancyForMunicipalCorporationsWardMembersDTO.setDateOfOccurrence(parsedDate);
			vacancyForMunicipalCorporationsWardMembersDTO.setReportingOfVacancy(parsedDateReporting);
			vacancyForMunicipalCorporationsWardMembersDTO.setVacancyFillDate(dateOfFilling);

			if (dateOfFilling != null) {
				vacancyForMunicipalCorporationsWardMembersRepo.updateVacancyForMunicipalForFillingData(districtId, assemblyId,
						vacancyForMunicipalCorporationsWardMembersDTO.getWardId(), vacancyForMunicipalCorporationsWardMembersDTO.getUlbName(), reason,
						parsedDate,parsedDateReporting, remarks, userId,
						vacancyForMunicipalCorporationsWardMembersDTO.getAnyImpediment(),
						eleRep,userId, id);
			} else {
				vacancyForMunicipalCorporationsWardMembersRepo.updateVacancyForMunicipal(districtId,assemblyId,
						vacancyForMunicipalCorporationsWardMembersDTO.getWardId(), vacancyForMunicipalCorporationsWardMembersDTO.getUlbName(), reason,
						parsedDate, parsedDateReporting, remarks, userId,
						vacancyForMunicipalCorporationsWardMembersDTO.getAnyImpediment(),
						eleRep,userId, id);

			}

			
			redirectAttr.addFlashAttribute("msg", "Vacancy Updated Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof AccessDeniedException) {
				throw (AccessDeniedException) e;
			}
			redirectAttr.addFlashAttribute("msgfail", "Failed to Update  ! Please try again....");
		}
		
		 

		return "redirect:/getVacancyForMunicipalCorporationsWardMembers";
	}

	@PostMapping("/checkOrdinaryElectionForUlbWard")
    public ResponseEntity<Boolean> checkOrdinaryElectionForUlbWard(@RequestParam("districtId") String districId,
			@RequestParam("ulbType") String ulbType,
			@RequestParam("ulbName") String ulbName,@RequestParam("assemblyId") String assemblyId,@RequestParam("wardId") String wardId,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence) {
	 Boolean inOrdinaryElection=false;
	 try {
		 
	 inOrdinaryElection = vacancyForMunicipalCorporationsWardMembersRepo.isInOrdinaryElectionForWard(districId,ulbType,ulbName,assemblyId,wardId,dateOfOccurrence);
	 
	 if(inOrdinaryElection==null) {
		 inOrdinaryElection=true;
	 }
	 
	 }catch(Exception e) {
		 e.printStackTrace();		
		 }
      
        return ResponseEntity.ok(inOrdinaryElection);
    }
	
		
	@Transactional
	@PostMapping(value = "/deleteVacancyForMunicipalCorporationsWard")
	public String deleteVacancyForMunicipalCorporationsWard(RedirectAttributes redirectAttr,
			@ModelAttribute("VacancyForMunicipalCorporationsWardMembersDTO") VacancyForMunicipalCorporationsWardMembersDTO vacancyForMunicipalCorporationsWardMembersDTO,
			HttpServletRequest request ) {
		try {
			int id = vacancyForMunicipalCorporationsWardMembersDTO.getId();
			vacancyForMunicipalCorporationsWardMembersRepo.deleteByIdNativeMc(id);
			redirectAttr.addFlashAttribute("msg", "Deleted Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Delete  ! Please try again....");
		}
		return "redirect:/getVacancyForMunicipalCorporationsWardMembers";
	}
}
