package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.pojo.OrdinaryVacancyCPRApprovalForGpDTO;
import cgg.gov.in.apsec.pojo.VacancyOrdinaryCPRElectionsMPPDTO;
import cgg.gov.in.apsec.repo.VacancyOrdinaryCPRElectionsMPPRepo;
import cgg.gov.in.apsec.service.VacancyOrdinaryCPRElectionsMPPService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyOrdinaryCPRElectionsMPPController {

	private static Logger logger = LoggerFactory.getLogger(VacancyOrdinaryCPRElectionsMPPController.class);

	@Autowired
	private VacancyOrdinaryCPRElectionsMPPService vacancyOrdinaryCPRElectionsMPPService;

	@Autowired
	private VacancyOrdinaryCPRElectionsMPPRepo vacancyOrdinaryCPRElectionsMPPRepo;

	@GetMapping(value = "/vacancyOrdinaryCPRElectionsMPP")
	public String ordinaryVacancyCPRApprovalForGp(Model model,
			@ModelAttribute("vacancyOrdinaryCPRElectionsMPPDto") VacancyOrdinaryCPRElectionsMPPDTO vacancyOrdinaryCPRElectionsMPPDto,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> districtList = vacancyOrdinaryCPRElectionsMPPService.getDistsData();
			model.addAttribute("districtList", districtList);
			model.addAttribute("admin", "admin");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyOrdinaryCPRElectionsMPP";
	}

	@PostMapping("fetchCPRMunicipalityNPData")
	public String fetchCPRMunicipalityNPData(Model model,
			@ModelAttribute("vacancyOrdinaryCPRElectionsMPPDto") VacancyOrdinaryCPRElectionsMPPDTO vacancyOrdinaryCPRElectionsMPPDto,
			RedirectAttributes redirectAttr) {
		try {
			List<Map<String, Object>> ordinaryVacancyCPRList = vacancyOrdinaryCPRElectionsMPPService
					.getMunNagarapanchayatCprData(vacancyOrdinaryCPRElectionsMPPDto);
			redirectAttr.addFlashAttribute("ordinaryVacancyCPRList", ordinaryVacancyCPRList);
			if (ordinaryVacancyCPRList.size() == 0) {
				redirectAttr.addFlashAttribute("failMessage", "Data Not Found");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/vacancyOrdinaryCPRElectionsMPP";
	}

	@RequestMapping(value = "/saveOrdVacMPPNPPCPRConfirmation", method = RequestMethod.POST)
	public String saveOrdVacMPPNPPCPRConfirmation(
			@ModelAttribute("vacancyOrdinaryCPRElectionsMPPDto") VacancyOrdinaryCPRElectionsMPPDTO vacancyOrdinaryCPRElectionsMPPDto,
			RedirectAttributes redirectAttr,HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			boolean isSaved = vacancyOrdinaryCPRElectionsMPPRepo
					.saveOrdVaccCPRMPNPConfirmation(vacancyOrdinaryCPRElectionsMPPDto.getSelectedItems(),userId);
			if (isSaved) {
				redirectAttr.addFlashAttribute("msg",
						" Submitted to SEC successfully.");
			} else {
				redirectAttr.addFlashAttribute("msgfail",
						"Failed to submit to SEC..!!");
			}
		} catch (Exception e) {
			logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/vacancyOrdinaryCPRElectionsMPP";
	}
	
	
	
	
	@PostMapping(value = "/ordinaryVacancyMPPCPRAbstractReport")
	public String ordinaryVacancyMPPCPRAbstractReport(Model model,
			@ModelAttribute("vacancyOrdinaryCPRElectionsMPPDto") VacancyOrdinaryCPRElectionsMPPDTO vacancyOrdinaryCPRElectionsMPPDto,HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			List<Map<String, Object>> ordinaryMPPCPRAbstractReport = vacancyOrdinaryCPRElectionsMPPService.ordinaryVacancyMPPCPRAbstractReportFromCeo(vacancyOrdinaryCPRElectionsMPPDto);
			model.addAttribute("ordinaryMPPCPRAbstractReport", ordinaryMPPCPRAbstractReport);
			
			model.addAttribute("vacancyOrdinaryCPRElectionsMPPDto", vacancyOrdinaryCPRElectionsMPPDto);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "ordinaryVacancyMPPCPRAbstractReport";
	}
	
	
	@PostMapping(value = "/getOrdinaryMPPWiseMpps")
	public String getOrdinaryMPPWiseMpps(
	        Model model,
	        @ModelAttribute("vacancyOrdinaryCPRElectionsMPPDto") VacancyOrdinaryCPRElectionsMPPDTO vacancyOrdinaryCPRElectionsMPPDto) {
	    try {
	      
	        List<Map<String, Object>> ordinaryCPRAbstractReportMppsWise = 
	        		vacancyOrdinaryCPRElectionsMPPService.getOrdinaryMPPWiseMpps(vacancyOrdinaryCPRElectionsMPPDto.getDistrictId());
	       
	            model.addAttribute("ordinaryCPRAbstractReportMppsWise", ordinaryCPRAbstractReportMppsWise);
	    
	        model.addAttribute("vacancyOrdinaryCPRElectionsMPPDto", vacancyOrdinaryCPRElectionsMPPDto);
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return "ordinaryVacancyMPPCPRAbstractReport"; // Return the view name
	}
	
	@PostMapping(value = "/getOrdinaryMPPWiseMppsCeo")
	public String getOrdinaryMPPWiseMppsCeo(
	        Model model,
	        @ModelAttribute("vacancyOrdinaryCPRElectionsMPPDto") VacancyOrdinaryCPRElectionsMPPDTO vacancyOrdinaryCPRElectionsMPPDto,
	        HttpServletRequest request) {
	    try {
	    	String token = TokenUtil.generateToken(request.getSession());
	    	model.addAttribute("formToken", token);
	    	 
	        List<Map<String, Object>> ordinaryCPRAbstractReportMppsWise = 
	        		vacancyOrdinaryCPRElectionsMPPService.getOrdinaryMPPWiseMppsCeo(vacancyOrdinaryCPRElectionsMPPDto.getDistrictId());
	       
	            model.addAttribute("ordinaryCPRAbstractReportMppsWise", ordinaryCPRAbstractReportMppsWise);
	    
	        model.addAttribute("vacancyOrdinaryCPRElectionsMPPDto", vacancyOrdinaryCPRElectionsMPPDto);
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return "ordinaryVacancyMPPCPRAbstractReport"; // Return the view name
	}
}