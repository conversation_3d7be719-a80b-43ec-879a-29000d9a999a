package cgg.gov.in.apsec.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyForPresidentEntity;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyForPresidentRepository;
import cgg.gov.in.apsec.service.ElectionProcessRuralLocalGovernment;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyForPresidentController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private VacancyForPresidentRepository vacancyRepo;

	@Autowired
	private ElectionProcessRuralLocalGovernment ruralLocalGovtservice;

	@GetMapping(value = "/vacancyForPresident")
	public String vacancyForPresident(Model model,
			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id().toString();
			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
			model.addAttribute("districtId", districtId);
			String mandalId = detailsByUserId.getMandalId();
			String revenueDivisionId = detailsByUserId.getRevenueDivisionId();
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);

			String revenueDivisionNameInt = userRepo.getRevenueDivisionNameInt(Integer.parseInt(revenueDivisionId),
					Integer.parseInt(districtId));
			model.addAttribute("revenueDivisionName", revenueDivisionNameInt);

			List<Map<String, Object>> assemblyConstituencyList = userRepo.findAssemblyConstituency(districtId,
					revenueDivisionId);
			model.addAttribute("assemblyConstituencyList", assemblyConstituencyList);

			List<Map<String, Object>> gramPanchayatList = userRepo.gramPanchayatList(districtId, revenueDivisionId,
					mandalId);
			model.addAttribute("listOfGps", gramPanchayatList);

			List<Map<String, Object>> mppList = ruralLocalGovtservice.getMPTCNames(districtId, revenueDivisionId,
					mandalId);
			model.addAttribute("mppList", mppList);

			model.addAttribute("mandalName", userRepo.getMandalName(districtId, revenueDivisionId, mandalId));
			model.addAttribute("mandalId", mandalId);
			model.addAttribute("revenueDivisionId", revenueDivisionId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForPresident";
	}

	@PostMapping(value = "/vacancyForPresidentSave")
	public String vacancyForPresidentSave(Model model,
			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			vacancyForPresidentEntity.setUpdatedBy(userId);

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForPresidentEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;

			if (vacancyForPresidentEntity.getVacancyFillDatee() != null
					&& !vacancyForPresidentEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForPresidentEntity.getVacancyFillDatee());
			}

			vacancyForPresidentEntity.setDateOfOccurrence(parsedDate);
			if (dateOfFilling != null) {
				vacancyForPresidentEntity.setVacancyFillDate(dateOfFilling);
			}
			Date dateOfReporting = dateFormat.parse(vacancyForPresidentEntity.getDateOfReportingVacancyee());
			vacancyForPresidentEntity.setDateOfReportingVacancy(dateOfReporting);
			vacancyRepo.save(vacancyForPresidentEntity);
			redirectAttr.addFlashAttribute("msg", "Vacancy for President Added Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Add President ! Please try again....");
		}
		return "redirect:/vacancyForPresident";
	}

	@GetMapping(value = "/getVacancyForPresident")
	public String getVacancyForPresident(Model model,
			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);

			List<Map<String, Object>> VacancyForPresident = vacancyRepo.getVacancyDetailsByDistrictAndMandal(userId);
			model.addAttribute("VacancyForPresident", VacancyForPresident);
			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForPresidentList";
	}

	@GetMapping(value = "/vacancyForPresidentUpdateForm")
	public String vacancyForPresidentUpdateForm(Model model,
			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			String mandalId = detailsByUserId.getMandalId();
			String rdId = detailsByUserId.getRevenueDivisionId();
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);

			List<Map<String, Object>> mppList = ruralLocalGovtservice.getMPTCNames(districtId, rdId, mandalId);
			model.addAttribute("mppList", mppList);

			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> VacancyForPresidentpdate = vacancyRepo.getVacancyUpdateForm(id);
			if (!VacancyForPresidentpdate.isEmpty()) {

				Map<String, Object> firstRow = VacancyForPresidentpdate.get(0);

				String districtid = (String) firstRow.get("district_id");
				vacancyForPresidentEntity.setDistrictId(districtid);
				model.addAttribute("district_id", districtid);

				String distName = userRepo.getDistrictName(districtid);
				model.addAttribute("districtName", distName);

				String rid = (String) firstRow.get("revenue_division_id");
				vacancyForPresidentEntity.setRevenueDivisionId(rid);
				model.addAttribute("revenue_division_id", rid);

				String revenueName = userRepo.getRevenueDivisionName(rid, districtid);
				model.addAttribute("revenueName", revenueName);

				String mandalId1 = (String) firstRow.get("mandal_id");
				vacancyForPresidentEntity.setMandalId(mandalId1);
				model.addAttribute("mandal_id", mandalId1);

				String mandalName = userRepo.getMandalName(districtid, rid, mandalId1);
				model.addAttribute("mandalName", mandalName);

				String officeName = (String) firstRow.get("office_name");
				vacancyForPresidentEntity.setOfficeName(officeName);
				model.addAttribute("office_name", officeName);

				String reason = (String) firstRow.get("reason_vacancy");
				vacancyForPresidentEntity.setReasonVacancy(reason);
				model.addAttribute("reason_vacancy", reason);

				String any = (String) firstRow.get("any_impediment");
				vacancyForPresidentEntity.setAnyImpediment(any);
				model.addAttribute("any_impediment", any);

				String remarks = (String) firstRow.get("remarks");
				vacancyForPresidentEntity.setRemarks(remarks);
				model.addAttribute("remarks", remarks);

				String any1 = (String) firstRow.get("any_vacancy");
				vacancyForPresidentEntity.setAnyVacancy(any1);
				model.addAttribute("any_vacancy", any1);

				String mandalconstituency = (String) firstRow.get("territorial_constituency");
				vacancyForPresidentEntity.setTerritorialConstituency(mandalconstituency);
				model.addAttribute("territorial_constituency", mandalconstituency);

				String elected = (String) firstRow.get("elected_representative_name");
				vacancyForPresidentEntity.setElectedRepresentativeName(elected);
				model.addAttribute("elected_representative_name", elected);

				java.sql.Date sqlDate = (java.sql.Date) firstRow.get("date_of_occurrence");
				java.sql.Date sqlDate3 = (java.sql.Date) firstRow.get("vacancy_fill_date");
				java.sql.Date sqlDate4 = (java.sql.Date) firstRow.get("date_of_reporting_vacancy");

				Date utilDate = new Date(sqlDate.getTime());

				Date utilDate3 = null;
				if (sqlDate3 != null && !sqlDate3.toString().trim().isEmpty()) {
					utilDate3 = new Date(sqlDate3.getTime());
				}

				Date utilDate4 = new Date(sqlDate4.getTime());
				SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				String dateText = dateFormat.format(utilDate);

				String dateText3 = null;
				if (utilDate3 != null) {
					dateText3 = dateFormat.format(utilDate3);
				}
				String dateText4 = dateFormat.format(utilDate4);
				vacancyForPresidentEntity.setDateOfOccurrence(utilDate);
				if (utilDate3 != null) {
					vacancyForPresidentEntity.setVacancyFillDate(utilDate3);
				}
				vacancyForPresidentEntity.setDateOfReportingVacancy(utilDate4);
				model.addAttribute("date_of_occurrence", dateText);
				if (dateText3 != null) {
					model.addAttribute("vacancy_fill_date", dateText3);
				}
				model.addAttribute("date_of_reporting_vacancy", dateText4);

				model.addAttribute("firstRow", firstRow);
			}
			model.addAttribute("populate", "populate");
			model.addAttribute("id", id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForPresident";
	}

	@Transactional
	@PostMapping(value = "/UpdateVacancyDirectForPresident")
	public String UpdateVacancyDirectForPresident(Model model,
			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			String districtId = vacancyForPresidentEntity.getDistrictId();
			String mandalId = vacancyForPresidentEntity.getMandalId();
			String rdId = vacancyForPresidentEntity.getRevenueDivisionId();
			String officeName = vacancyForPresidentEntity.getOfficeName();
			String reason = vacancyForPresidentEntity.getReasonVacancy();
			String any = vacancyForPresidentEntity.getAnyImpediment();
			String remarks = vacancyForPresidentEntity.getRemarks();
			String elected = vacancyForPresidentEntity.getElectedRepresentativeName();

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForPresidentEntity.getDateOfOccurrencee());
			Date dateOfReporting = dateFormat.parse(vacancyForPresidentEntity.getDateOfReportingVacancyee());
			Date dateOfFilling = null;

			if (vacancyForPresidentEntity.getVacancyFillDatee() != null
					&& !vacancyForPresidentEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForPresidentEntity.getVacancyFillDatee());
			}

			if (dateOfFilling != null) {
				vacancyRepo.updateVacancyForPresident(districtId, mandalId, officeName, rdId, reason, parsedDate, any,
						dateOfFilling, remarks, vacancyForPresidentEntity.getAnyVacancy(),
						vacancyForPresidentEntity.getTerritorialConstituency(), userId, elected,dateOfReporting, id);

			} else {
				vacancyRepo.updateVacancyForPresidentForFilledDate(districtId, mandalId, officeName, rdId, reason,
						parsedDate, any, remarks, vacancyForPresidentEntity.getAnyVacancy(),
						vacancyForPresidentEntity.getTerritorialConstituency(), userId, elected,dateOfReporting, id);

			}
			vacancyForPresidentEntity.setDateOfOccurrence(parsedDate);
			vacancyForPresidentEntity.setVacancyFillDate(dateOfFilling);
			vacancyForPresidentEntity.setDateOfReportingVacancy(dateOfReporting);
			redirectAttr.addFlashAttribute("msg", "Vacancy for President Updated Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Update  ! Please try again....");
		}
		return "redirect:/getVacancyForPresident";
	}

	@Transactional
	@GetMapping(value = "/deleteVacancyForPresident")
	public String deleteVacancyForPresident(RedirectAttributes redirectAttr,
			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
			@RequestParam(name = "id", required = false) Integer id) {
		try {
			vacancyRepo.deleteByIdNative(id);
			redirectAttr.addFlashAttribute("msg", "Vacancy for President Deleted Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Delete  ! Please try again....");
		}
		return "redirect:/getVacancyForPresident";
	}
	
	
//	@GetMapping(value = "/vacancyForPresidentCeo")
//	public String vacancyForPresidentCeo(Model model,
//			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
//			HttpServletRequest request) {
//		try {
//			String userId = (String) request.getSession().getAttribute("loggedInUser");
//			User detailsByUserId = userRepo.getDetailsByUserId(userId);
//			String districtId = detailsByUserId.getDistrict_id().toString();
//			model.addAttribute("districtName", userRepo.getDistrictName(districtId));
//			model.addAttribute("districtId", districtId);
//			String mandalId = detailsByUserId.getMandalId();
//			String revenueDivisionId = detailsByUserId.getRevenueDivisionId();
//			List<Map<String, Object>> rdList = userRepo.findRevenueDivisionBasedOnDistrictInt(Integer.parseInt(districtId));
//			model.addAttribute("listOfRevenueDivisions", rdList);
//
//			List<Map<String, Object>> assemblyConstituencyList = userRepo.findAssemblyConstituency(districtId,
//					revenueDivisionId);
//			model.addAttribute("assemblyConstituencyList", assemblyConstituencyList);
//
//			List<Map<String, Object>> gramPanchayatList = userRepo.gramPanchayatList(districtId, revenueDivisionId,
//					mandalId);
//			model.addAttribute("listOfGps", gramPanchayatList);
//
//			List<Map<String, Object>> mppList = ruralLocalGovtservice.getMPTCNames(districtId, revenueDivisionId,
//					mandalId);
//			model.addAttribute("mppList", mppList);
//
//			model.addAttribute("mandalName", userRepo.getMandalName(districtId, revenueDivisionId, mandalId));
//			model.addAttribute("mandalId", mandalId);
//			model.addAttribute("revenueDivisionId", revenueDivisionId);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return "vacancyForPresidentCEO";
//	}
//
//	@PostMapping(value = "/ceoVacancyForPresidentSave")
//	public String ceoVacancyForPresidentSave(Model model,
//			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
//			HttpServletRequest request, RedirectAttributes redirectAttr) {
//		try {
//			String userId = (String) request.getSession().getAttribute("loggedInUser");
//
//			vacancyForPresidentEntity.setUpdatedBy(userId);
//
//			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
//			Date parsedDate = dateFormat.parse(vacancyForPresidentEntity.getDateOfOccurrencee());
//			Date dateOfFilling = null;
//
//			if (vacancyForPresidentEntity.getVacancyFillDatee() != null
//					&& !vacancyForPresidentEntity.getVacancyFillDatee().isEmpty()) {
//				dateOfFilling = dateFormat.parse(vacancyForPresidentEntity.getVacancyFillDatee());
//			}
//
//			vacancyForPresidentEntity.setDateOfOccurrence(parsedDate);
//			if (dateOfFilling != null) {
//				vacancyForPresidentEntity.setVacancyFillDate(dateOfFilling);
//			}
//
//			vacancyRepo.save(vacancyForPresidentEntity);
//			redirectAttr.addFlashAttribute("msg", "Vacancy for President Added Successfully....");
//		} catch (Exception e) {
//			e.printStackTrace();
//			redirectAttr.addFlashAttribute("msgfail", "Failed to Add President ! Please try again....");
//		}
//		return "redirect:/vacancyForPresidentCeo";
//	}
//	
//	
//	@GetMapping(value = "/getVacancyForCEOPresident")
//	public String getVacancyForCEOPresident(Model model,
//			@ModelAttribute("vacancyForPresidentEntity") VacancyForPresidentEntity vacancyForPresidentEntity,
//			HttpServletRequest request, RedirectAttributes redirectAttr) {
//		try {
//			String userId = (String) request.getSession().getAttribute("loggedInUser");
//
//			List<Map<String, Object>> VacancyForPresident = vacancyRepo.getVacancyDetailsByDistrictAndMandal(userId);
//			model.addAttribute("VacancyForPresident", VacancyForPresident);
//			model.addAttribute("now", new Date());
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return "vacancyForPresidentListCEO";
//	}
}