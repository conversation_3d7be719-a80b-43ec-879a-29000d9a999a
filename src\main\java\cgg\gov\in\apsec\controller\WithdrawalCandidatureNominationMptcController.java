package cgg.gov.in.apsec.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationDTO;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationMptcDTO;
import cgg.gov.in.apsec.pojo.WithdrawalWardCandidatureNominationDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationMptcRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationZptcRepository;
import cgg.gov.in.apsec.service.WithdrawalCandidatureNominationMptcService;
import cgg.gov.in.apsec.service.WithdrawalCandidatureNominationService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class WithdrawalCandidatureNominationMptcController {
	
	@Autowired
	private WithdrawalCandidatureNominationMptcService withdrawalCandidatureNominationMptcService;

	@Autowired
	private UserRepository userRepo;
	
	@Autowired
	private WithdrawalCandidatureNominationMptcRepository withdrawalCandidatureNominationMptcRepository;
	
	@Autowired
	private WithdrawalCandidatureNominationZptcRepository withdrawalCandidatureNominationZptcRepository;

	
	@RequestMapping(value = "/withdrawalCandidatureNominationForMptc")
	public String withdrawalCandidatureNominationForMptc(Model model, HttpServletRequest request,
			@ModelAttribute("withdrawalCandidatureNominationMptcDto") WithdrawalCandidatureNominationMptcDTO withdrawalCandidatureNominationMptcDto) {
		 
			List<Map<String, Object>> withDrawalNominationList = null;
		    List<Map<String, Object>> withDrawalStatusList = null;
		try {

			String userId = request.getSession().getAttribute("loggedInUser").toString();
	        User detailsByUserId = userRepo.getDetailsByUserId(userId);
	        String districtId = detailsByUserId.getDistrict_id().toString();
	        String revenueId = detailsByUserId.getRevenueDivisionId();
	        String mandalId = detailsByUserId.getMandalId();
	        String gpcode = detailsByUserId.getGpcode();
	        String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);

	        model.addAttribute("districtId", districtId);
            model.addAttribute("revenueDivisionId", revenueId);
            model.addAttribute("mandalId", mandalId);
            model.addAttribute("gpCode",  gpcode);
            
			User user = userRepo.getDetailsByUserId(userId);
			 List<Map<String, Object>> districts1 = userRepo.findDistricts();
			    model.addAttribute("districtListZPP", districts1);

			if (user.getDistrict_id() == null || user.getDistrict_id().isEmpty() || user.getDistrict_id().equals("0")) {
			    List<Map<String, Object>> districts = userRepo.findDistricts();
			    model.addAttribute("districtList", districts);
			} else {
			    model.addAttribute("userDistrictId", user.getDistrict_id());
			    String districtName = userRepo.getDistrictNameInt(Integer.parseInt(user.getDistrict_id()));
			    model.addAttribute("userDistrictName", districtName);
			    if (user.getRevenueDivisionId() == null || user.getRevenueDivisionId().equals("0")) {
			        model.addAttribute("userDistrictName", districtName);
			        List<Map<String, Object>> revenues = userRepo.findRevenueDivisionBasedOnDistrictInt(Integer.parseInt(user.getDistrict_id()));
			        model.addAttribute("revenueDivisionsList", revenues);
			    } else {
			        model.addAttribute("userDistrictName", districtName);
			        model.addAttribute("userRevenueDivisionId", user.getRevenueDivisionId());
			        String revenueDivisionNameInt = userRepo.getRevenueDivisionNameInt(Integer.parseInt(user.getRevenueDivisionId()), Integer.parseInt(user.getDistrict_id()));
			        model.addAttribute("userRevenueDivisionName", revenueDivisionNameInt);
			        if (user.getMandalId() == null || user.getMandalId().equals("0")) {
			            List<Map<String, Object>> mandaList = userRepo.getMPPList(user.getDistrict_id(),user.getRevenueDivisionId());
			            model.addAttribute("mppList", mandaList);
			        } else {
			            String mandalName = userRepo.getMandalNameInt(Integer.parseInt(user.getDistrict_id()), Integer.parseInt(user.getRevenueDivisionId()), Integer.parseInt(mandalId));
			            model.addAttribute("userMppId", mandalId);
			            model.addAttribute("userMppName", mandalName);
			            if(user.getGpcode()==null || user.getGpcode().equals("0") ||user.getGpcode().equals("")) {
			            List<Map<String, Object>> gpList = userRepo.gramPanchayatListGP(user.getDistrict_id(),user.getRevenueDivisionId(),mandalId);
			            model.addAttribute("gpList", gpList);
			            }
			            else {
			            	model.addAttribute("roGpCode",user.getGpcode());
			            	model.addAttribute("roGpName",userRepo.getGPName(user.getDistrict_id(),
			            			user.getRevenueDivisionId(), mandalId, user.getGpcode()));
			            	List<Map<String, Object>> wards = userRepo.getWardsNo(Integer.parseInt(user.getDistrict_id()), Integer.parseInt(user.getMandalId()),
			            			Integer.parseInt(user.getGpcode()));
			            	model.addAttribute("wardNos",wards);
			            }
			        }
			    }
			}
			
			List<Map<String, Object>> mptc = userRepo.getMptcList(user.getDistrict_id(),user.getRevenueDivisionId(),user.getMandalId());
        	model.addAttribute("mptcList",mptc);
            
            String mptcCode = (String) model.asMap().get("mptcCode");
            withdrawalCandidatureNominationMptcDto.setMptcCode(mptcCode);
	        if (mptcCode == null || mptcCode.equals("0")) {
	           // wardwithDrawalNominationList = withdrawalWardCandidatureNominationService.getwithdrawalNominationDataList(districtId, revenueId, mandalId, gpcode);
	           // wardwithDrawalStatusList = withdrawalWardCandidatureNominationService.getwardwithDrawaStatusList(districtId, mandalId, gpcode);
	        } else {
	        	 withDrawalNominationList = withdrawalCandidatureNominationMptcService
	 					.getwithdrawalNominationDataListMptc(districtId, revenueId, mandalId, mptcCode);
	 			model.addAttribute("withDrawalNominationList", withDrawalNominationList);

	 			withDrawalStatusList = withdrawalCandidatureNominationMptcService.getWithDrawaStatusListMptc(districtId, mandalId,
	 					revenueId,mptcCode);
	 			model.addAttribute("withDrawalStatusList", withDrawalStatusList);
	            model.addAttribute("slectedMptcCode", mptcCode);
	        }
            
            
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "withdrawalCandidatureNominationForMptc";
	}

	@PostMapping("/getDetailsForMptcWithDraw")
	public String getDetailsForMptcWithDraw(RedirectAttributes redirectAttr,
	        @ModelAttribute("withdrawalCandidatureNominationMptcDto") WithdrawalCandidatureNominationMptcDTO withdrawalCandidatureNominationMptcDto, HttpServletRequest request)  throws Exception
{
	    try {
	    	String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			 
			
			if(user.getDistrict_id()!=null && !user.getDistrict_id().isEmpty() &&  !"0".equals(user.getDistrict_id()) )
			{
				if(!withdrawalCandidatureNominationMptcDto.getDistrictId().equals(user.getDistrict_id())) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			}
			if(user.getMandalId()!=null && !user.getMandalId().isEmpty() && !"0".equals(user.getMandalId()))
			{
				if(!withdrawalCandidatureNominationMptcDto.getMandalId().equals(user.getMandalId()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			} 
			if(user.getRevenueDivisionId()!=null && !user.getRevenueDivisionId().isEmpty() && !"0".equals(user.getRevenueDivisionId()) )
			{
				if(!withdrawalCandidatureNominationMptcDto.getRevenueDivisionId().equals(user.getRevenueDivisionId()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			} 
			
			 
	        redirectAttr.addFlashAttribute("mptcCode", withdrawalCandidatureNominationMptcDto.getMptcCode().split("_")[0]);
	    } catch(Exception e) {
			
			if (e instanceof AccessDeniedException) {
				throw e;
			}
			e.printStackTrace();
		} 
	    return "redirect:/withdrawalCandidatureNominationForMptc";
	}
	
	@PostMapping("mptcwithdrawnStatus")
	@ResponseBody
	public ResponseEntity<Map<String, Object>> withdrawnStatus(
	        @ModelAttribute("withdrawalCandidatureNominationMptcDto") WithdrawalCandidatureNominationMptcDTO withdrawalCandidatureNominationMptcDto,
	        @RequestParam("slnoList") String slnoList,
	        @RequestParam("district_Id") String districtId,
	        @RequestParam("revenueDivision_Id") String revenueDivisionId,
	        @RequestParam("mandal_Id") String mandalId,
	        @RequestParam("mptc_Id") String mptcCode,
	        @RequestParam("processComplete") int processComplete) {

	    Map<String, Object> response = new HashMap<>();
	    try {
	        ObjectMapper objectMapper = new ObjectMapper();
	        List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
	                new TypeReference<List<Map<String, String>>>() {});
	        
	        int underscoreIndex = mptcCode.indexOf('_');
	        String partBeforeUnderscore = mptcCode.substring(0, underscoreIndex); 
	        mptcCode = partBeforeUnderscore;

	        // Update withdrawal nominations
	        int result = withdrawalCandidatureNominationMptcService.updateMptcWithdrawalNomination(slnoListMap);
	        if (result > 0) {
	            response.put("success", true);
	            response.put("message", "Withdrawal process completed successfully.");
	        } else {
	            response.put("success", false);
	            response.put("message", "Failed to complete the withdrawal process.");
	            return ResponseEntity.ok(response);
	        }

	        // Check if data is already confirmed
	        int existingCount = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcConfirmCountAjax(
	                districtId, revenueDivisionId, mandalId, mptcCode);
	        if (existingCount > 0) {
	            response.put("success", false);
	            response.put("message", "Data already confirmed!");
	            return ResponseEntity.ok(response);
	        }

	        // Process completion confirmation
	        int roCount = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcConfirmByROAjax(
	                districtId, revenueDivisionId, mandalId, mptcCode, processComplete);
	        if (roCount > 0) {
	            response.put("success", true);
	            response.put("message", "Process completed and data updated.");
	        } else {
	            response.put("success", false);
	            response.put("message", "No data updated during process completion.");
	        }
	    } catch (Exception e) {
	        e.printStackTrace();
	        response.put("success", false);
	        response.put("message", "An error occurred during processing: " + e.getMessage());
	    }

	    return ResponseEntity.ok(response);
	}
	
	@RequestMapping(value = "/receiptMptcAcknowledged")
	public String receiptMptcAcknowledged(Model model, @RequestParam("slno") String slno) {
		try {
			model.addAttribute("slno", slno);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "receiptAcknowledgedforMptc";
	}
	
	@RequestMapping(value = "/reportAKWMptcData")
	 public ResponseEntity<String> reportACKFormMptcData(Model model, 
			 @ModelAttribute("withdrawalCandidatureNominationMptcDto") WithdrawalCandidatureNominationMptcDTO withdrawalCandidatureNominationMptcDto,            
        RedirectAttributes redirectAttr,HttpServletRequest request,
          @RequestParam("akwcandidateSigned") String akwcandidateSigned,
          @RequestParam("akwbywhomemptcformSubmite") String akwbywhomemptcformSubmite,
          @RequestParam("dateFieldOne") String dateFieldOne,
          @RequestParam("timeFieldTwo") String timeFieldTwo,
          @RequestParam("slno") Integer slno) {	     
	     try {
	    	 String userId = request.getSession().getAttribute("loggedInUser").toString();
	    	 WithdrawalCandidatureNominationMptcDTO dto=new WithdrawalCandidatureNominationMptcDTO();
	    	 dto.setAkwmptccandidateSigned(akwcandidateSigned);
	    	 dto.setAkwbywhomemptcformSubmite(akwbywhomemptcformSubmite);
	    	
	    	 dto.setDateFieldOne(dateFieldOne);
	    	 dto.setTimeFieldTwo(timeFieldTwo);
			int result = withdrawalCandidatureNominationMptcService
					.insertreportAKWFormInfo(withdrawalCandidatureNominationMptcDto, userId, slno.toString(),akwcandidateSigned,akwbywhomemptcformSubmite);
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess", " ReportAcknowlwdged Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed ReportAcknowlwdged Data ...!!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	     return ResponseEntity.ok("Success");
	}


	@RequestMapping(value = "/fetchMptcFormAKWInfo")
	public String fetchMptcFormAKWInfo(HttpServletRequest request ,Model model, @RequestParam("slno") String slno) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String districtId = user.getDistrict_id().toString();
			String revenueId = user.getRevenueDivisionId();
			String mandalId = user.getMandalId();
			
			String mandalName = withdrawalCandidatureNominationZptcRepository.getMandalName(districtId, revenueId, mandalId);
			model.addAttribute("userMppId", mandalId);
			model.addAttribute("userMppName", mandalName);

			String MptcName = withdrawalCandidatureNominationMptcRepository.akwMptcCodeName(slno);
			model.addAttribute("MptcName",MptcName);

			String  akwmptcCandidateName =  withdrawalCandidatureNominationMptcService.akwMptcName(slno);
			model.addAttribute("akwmptcCandidateName", akwmptcCandidateName);

			String  akwcandidateDate =  withdrawalCandidatureNominationMptcService.akwDate(slno);
			model.addAttribute("akwcandidateDate", akwcandidateDate);

			String  akwcandidateTime =  withdrawalCandidatureNominationMptcService.akwTime(slno);
			model.addAttribute("akwcandidateTime", akwcandidateTime);

			String akwbywhomeformSubmit = withdrawalCandidatureNominationMptcService.akwbywhomeformSubmit(slno);
			model.addAttribute("akwbywhomeformSubmit", akwbywhomeformSubmit);
			String whomSubmitted="";
			if(akwbywhomeformSubmit.equals("1")) {
				whomSubmitted=	akwmptcCandidateName;
			}
			if(akwbywhomeformSubmit.equals("2")) {
				whomSubmitted=userRepo.whomSubmitted_proposer_mptc(slno);
			}
			if(akwbywhomeformSubmit.equals("3")) {
				whomSubmitted=userRepo.whomSubmitted_election_agent_ward(user.getDistrict_id(),user.getMandalId(), user.getGpcode());
			}
			model.addAttribute("whomSubmitted",whomSubmitted);
			
			String roUploadsignature = userRepo.getMptcROUploadsignature(userId);
			model.addAttribute("roUploadsignature", roUploadsignature);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchFormforMptcReportAcknowledgeTel";
	}

@RequestMapping(value = "/getWithdrawlMptcConfirmCountAjax", method = RequestMethod.POST)
public @ResponseBody int getWithdrawlMptcConfirmCountAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
		@RequestParam(value = "mandalId") String mandalId,  @RequestParam(value = "mptcCode") String mptcCode) {
	int count = 0;
	try { 
		 int underscoreIndex = mptcCode.indexOf('_');
	        String partBeforeUnderscore = mptcCode.substring(0, underscoreIndex); 
	        mptcCode = partBeforeUnderscore;
		count = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcConfirmCountAjax1(districtId, revenueDivisionId, mandalId,mptcCode);
	} catch (Exception e) {
		e.printStackTrace();
	}
	return count;
}

@RequestMapping(value = "/getWithdrawlMptcConfirmByROAjax", method = RequestMethod.POST)
public @ResponseBody int getWithdrawlMptcConfirmByROAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
		@RequestParam(value = "mandalId") String mandalId,  @RequestParam(value = "mptcCode") String mptcCode,
		@RequestParam(value = "processComplete") int processComplete) {
	int count = 0;
	try {
		 int underscoreIndex = mptcCode.indexOf('_');
	        String partBeforeUnderscore = mptcCode.substring(0, underscoreIndex); 
	        mptcCode = partBeforeUnderscore;
			if(processComplete == 1) {
				count = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcConfirmByROAjax1(districtId, revenueDivisionId, mandalId,mptcCode, processComplete);
			}else if(processComplete == 2){
				int processCount = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcProcessCount(districtId, revenueDivisionId, mandalId,mptcCode);
				if(processCount > 0) {
					int updateCount = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcConfirmByROAjax1(districtId, revenueDivisionId, mandalId,mptcCode, processComplete);
					if(updateCount > 0) {
						count = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcDraftCount(districtId, revenueDivisionId, mandalId,mptcCode);
					}	
				}
			}else if(processComplete == 3){
				int draftCount =  withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcDraftCount(districtId, revenueDivisionId, mandalId, mptcCode);
				if(draftCount > 0) {
					count = withdrawalCandidatureNominationMptcRepository.getWithdrawlMptcConfirmByROAjax1(districtId, revenueDivisionId, mandalId, mptcCode, processComplete);
				}
			}
		
	} catch (Exception e) {
		e.printStackTrace();
	}
	return count;
}

@RequestMapping(value = "/fetchForm8MptcData", method = RequestMethod.GET)
public String fetchForm8MptcData(HttpServletRequest request, @RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
		@RequestParam(value = "mandalId") String mandalId,  @RequestParam(value = "mptcCode") String mptcCode, Model model)  throws Exception{
	try {
		String userId = request.getSession().getAttribute("loggedInUser").toString();
 		User detailsByUserId = userRepo.getDetailsByUserId(userId);
 		String token = TokenUtil.generateToken(request.getSession());
		model.addAttribute("formToken", token);


			String userdistrictId = detailsByUserId.getDistrict_id().toString();
			String userrevenueId = detailsByUserId.getRevenueDivisionId().toString(); 

 
			if(districtId!=null && !districtId.isEmpty() && !"0".equals(districtId))
			{
				if(! districtId.equals(userdistrictId)) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			}
			if(revenueDivisionId!=null && !revenueDivisionId.isEmpty() && !"0".equals(revenueDivisionId))
			{
				if(!revenueDivisionId.equals( detailsByUserId.getRevenueDivisionId()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			}
			if( mandalId!=null && !mandalId.isEmpty() && !"0".equals(mandalId))
			{
				if(!mandalId.equals( detailsByUserId.getMandalId()) ) {
					throw new AccessDeniedException("You are not authorized to view this data.");
				}
			}
		String districtName = userRepo.getDistrictName(districtId);
		User user = userRepo.getDetailsByUserId(userId);
		 int underscoreIndex = mptcCode.indexOf('_');
	        String partBeforeUnderscore = mptcCode.substring(0, underscoreIndex); 
	        mptcCode = partBeforeUnderscore;

	    String mandalName = withdrawalCandidatureNominationZptcRepository.getMandalName(districtId, revenueDivisionId, mandalId);
		model.addAttribute("userMppId", mandalId);
		model.addAttribute("userMppName", mandalName);

		String MptcName = userRepo.getTelMptcName(districtId, revenueDivisionId, mandalId,mptcCode);
		model.addAttribute("MptcName",MptcName);
		
		List<Map<String, Object>> formeightData =
				withdrawalCandidatureNominationMptcRepository.getFormVIIImptcdatanew(districtId, revenueDivisionId, mandalId,mptcCode);

		//String roUploadsignature = userRepo.getMptcROUploadsignature(userId);
		//model.addAttribute("roUploadsignature", roUploadsignature);
		

		model.addAttribute("formeightDataList", formeightData);
	} catch (Exception e) {
		e.printStackTrace();
		if (e instanceof AccessDeniedException) {
			throw e;
		}
	}
	return "fetchFormVIIIMptcforTelugu";
}
}
