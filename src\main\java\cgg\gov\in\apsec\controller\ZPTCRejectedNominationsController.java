package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.MPTCRejectedNominationsDTO;
import cgg.gov.in.apsec.pojo.ZPTCRejectedNominationsDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.ZPTCRejectedNominationsService;

@Controller
public class ZPTCRejectedNominationsController {

	@Autowired
	private ZPTCRejectedNominationsService zptcRejectedNominationsService;

	@Autowired
	private UserRepository userRepo;

	@RequestMapping(value = "/zptcRejectedNominations")
	public String zptcRejectedNominations(HttpServletRequest request, Model model,
			@ModelAttribute("zptcRejectedNominationsDTO") ZPTCRejectedNominationsDTO zptcRejectedNominationsDTO) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String distId = detailsByUserId.getDistrict_id();

			int districtId = Integer.parseInt(detailsByUserId.getDistrict_id());

			String districtName = userRepo.getDistrictNameInt(districtId);
			model.addAttribute("districtName", districtName);

			//List<Map<String, Object>> elections = zptcRejectedNominationsService.getElections();
			//model.addAttribute("electionsList", elections);
			
			List<Map<String, Object>> elections = userRepo.getElectionsForDistrictLevelForZPTC();
			model.addAttribute("electionsList", elections);


			 List<Map<String, Object>> zptcList = userRepo.getZPTCListbydistrictId(distId);
			model.addAttribute("zptcList", zptcList);

			model.addAttribute("districtId", detailsByUserId.getDistrict_id());
			model.addAttribute("mptcRejectedNominationsDTO", zptcRejectedNominationsDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "zptcRejectedNominations";
	}

	@RequestMapping(value = "/zptcRejectedNominationsReport")
	public String zptcRejectedNominationsReport(Model model, HttpServletRequest request,
			@ModelAttribute("zptcRejectedNominationsDTO") ZPTCRejectedNominationsDTO zptcRejectedNominationsDTO) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String distId = detailsByUserId.getDistrict_id();

			List<Map<String, Object>> zptcRejectedNominationsReport = zptcRejectedNominationsService
					.zptcRejectedNominationsReport(zptcRejectedNominationsDTO);
			model.addAttribute("zptcRejectedNominationsReport", zptcRejectedNominationsReport);

			int districtId1 = Integer.parseInt(detailsByUserId.getDistrict_id());

			String districtName = userRepo.getDistrictNameInt(districtId1);
			model.addAttribute("districtName", districtName);

			model.addAttribute("districtId", detailsByUserId.getDistrict_id());

			List<Map<String, Object>> elections = userRepo.getElectionsForDistrictLevelForZPTC();
			model.addAttribute("electionsList", elections);

			 List<Map<String, Object>> zptcList = userRepo.getZPTCListbydistrictId(distId);
				model.addAttribute("zptcList", zptcList);
				
				zptcRejectedNominationsDTO.setDistrictId(detailsByUserId.getDistrict_id());
				zptcRejectedNominationsDTO.setElectionCode(zptcRejectedNominationsDTO.getElectionCode());
				zptcRejectedNominationsDTO.setZPTCCode(zptcRejectedNominationsDTO.getZPTCCode());
				model.addAttribute("zptcRejectedNominationsDTO",zptcRejectedNominationsDTO);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "zptcRejectedNominations";
	}
	
	
	@RequestMapping(value = "/saveZPTCRejectedNominations")
	public String saveZPTCRejectedNominations(Model model, HttpServletRequest request,
			@ModelAttribute("zptcRejectedNominationsDTO") ZPTCRejectedNominationsDTO zptcRejectedNominationsDTO,RedirectAttributes redirect) {
		try {
		String userId = request.getSession().getAttribute("loggedInUser").toString();
				int count=	(int) zptcRejectedNominationsService.updatemptcScrutinyRejectionStatus(zptcRejectedNominationsDTO, 
				userId, request.getRemoteAddr());
		
		if (count > 0) {
			redirect.addFlashAttribute("saveSuccess", "Saved and sent to RO successfully!");
		} else {
			redirect.addFlashAttribute("saveFailure", "Failed to save!");
		}
		zptcRejectedNominationsDTO.setDistrictId(zptcRejectedNominationsDTO.getDistrictId());
		zptcRejectedNominationsDTO.setElectionCode(zptcRejectedNominationsDTO.getElectionCode());
		zptcRejectedNominationsDTO.setZPTCCode(zptcRejectedNominationsDTO.getZPTCCode());
		redirect.addFlashAttribute("zptcRejectedNominationsDTO",zptcRejectedNominationsDTO);
		
		}catch(Exception e) {
			e.printStackTrace();
		}
		
				return "redirect:/zptcRejectedNominationsReport";
		
	}
	
	
	
	
	
	
	
}