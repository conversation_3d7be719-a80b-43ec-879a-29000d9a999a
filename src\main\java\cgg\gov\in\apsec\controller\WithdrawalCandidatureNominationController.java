package cgg.gov.in.apsec.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationRepository;
import cgg.gov.in.apsec.service.WithdrawalCandidatureNominationService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class WithdrawalCandidatureNominationController {

	@Autowired
	private WithdrawalCandidatureNominationService withdrawalCandidatureNominationService;

	@Autowired
	private UserRepository userRepo;
	
	@Autowired
	private WithdrawalCandidatureNominationRepository withdrawalCandidatureNominationRepository;


	@RequestMapping(value = "/withdrawalCandidatureNomination")
	public String withdrawalCandidatureNomination(Model model, HttpServletRequest request,
			@ModelAttribute("withdrawalCandidatureNominationDto") WithdrawalCandidatureNominationDTO withdrawalCandidatureNominationDto) {
		List<Map<String, Object>> withDrawalNominationList = null;
		List<Map<String, Object>> withDrawalStatusList = null;
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id().toString();
			String revenueId = detailsByUserId.getRevenueDivisionId();
			String mandalId = detailsByUserId.getMandalId();
			String gpcode = detailsByUserId.getGpcode();
			
            model.addAttribute("districtId", districtId);
            model.addAttribute("revenueDivisionId", revenueId);
            model.addAttribute("mandalId", mandalId);
            model.addAttribute("gpCode",  gpcode);

			withDrawalNominationList = withdrawalCandidatureNominationService
					.getwithdrawalNominationDataList(districtId, revenueId, mandalId, gpcode);
			model.addAttribute("withDrawalNominationList", withDrawalNominationList);

			withDrawalStatusList = withdrawalCandidatureNominationService.getwithDrawaStatus(districtId, mandalId,
					gpcode);
			model.addAttribute("withDrawalStatusList", withDrawalStatusList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "withdrawalCandidatureNomination";
	}

/*	@PostMapping("withdrawnStatus")
	public String withdrawnStatus(RedirectAttributes redirectAttr,
			@ModelAttribute("withdrawalCandidatureNominationDto") WithdrawalCandidatureNominationDTO withdrawalCandidatureNominationDto,
			@RequestParam("slnoList") String slnoList) {
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
					new TypeReference<List<Map<String, String>>>() {
					});
			int result = withdrawalCandidatureNominationService.updateWithdrawalNomination(slnoListMap);
			if (result > 0) {
				redirectAttr.addFlashAttribute("updateSuccess", " With Drawan Has Done");
			} else {
				redirectAttr.addFlashAttribute("updateFailure", " Failed to With Drawan ");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/withdrawalCandidatureNomination";
	}
*/
	
	
	@PostMapping("withdrawnStatus")
	public String withdrawnStatus(
	        RedirectAttributes redirectAttr,
	        @ModelAttribute("withdrawalCandidatureNominationDto") WithdrawalCandidatureNominationDTO withdrawalCandidatureNominationDto,
	        @RequestParam("slnoList") String slnoList,
	        @RequestParam("districtId") String districtId,
	        @RequestParam("revenueDivisionId") String revenueDivisionId,
	        @RequestParam("mandalId") String mandalId,
	        @RequestParam("gpCode") String grampanchayatId,
	        @RequestParam("processComplete") int processComplete) {
	    ObjectMapper objectMapper = new ObjectMapper();
	    try {
	    	
	    	 List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
		                new TypeReference<List<Map<String, String>>>() {});
		        System.out.println(slnoListMap);
		        int result = withdrawalCandidatureNominationService.updateWithdrawalNomination(slnoListMap);

		        if (result > 0) {
		            redirectAttr.addFlashAttribute("updateSuccess", "Withdrawal has been done successfully.");
		        } else {
		            redirectAttr.addFlashAttribute("updateFailure", "Failed to withdraw.");
		        }
	        int existingCount = withdrawalCandidatureNominationRepository.getWithdrawlConfirmCountAjax(
	                districtId, revenueDivisionId, mandalId, grampanchayatId);
	        if (existingCount > 0) {
	            redirectAttr.addFlashAttribute("updateFailure", "Data Already Confirmed!");
	            return "redirect:/withdrawalCandidatureNomination";
	        }

	        int roCount = withdrawalCandidatureNominationRepository.getWithdrawlConfirmByROAjax(
	                districtId, revenueDivisionId, mandalId, grampanchayatId,processComplete);
	        if (roCount > 0) {
	            //redirectAttr.addFlashAttribute("updateSuccess", "Withdrawal has been done successfully.");
	            return "redirect:/withdrawalCandidatureNomination";
	        }

	       
	    } catch (Exception e) {
	        e.printStackTrace();
	        redirectAttr.addFlashAttribute("updateFailure", "An error occurred during processing.");
	    }
	    return "redirect:/withdrawalCandidatureNomination";
	}

	@RequestMapping(value = "/receiptAcknowledged")
	public String receiptAcknowledged(Model model, @RequestParam("slno") String slno) {
		try {
			model.addAttribute("slno", slno);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "receiptAcknowledgedforNomination";
	}

	@RequestMapping(value = "/reportAKWData")
	public ResponseEntity<String> reportAKWData(HttpServletRequest request, RedirectAttributes redirectAttr,
			@RequestParam("akwcandidateSigned") String akwcandidateSigned,
			@RequestParam("akwbywhomeformSubmit") String akwbywhomeformSubmit,
			@RequestParam("dateField") String dateField, @RequestParam("timeField") String timeField,
			@RequestParam("slno") Integer slno) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			WithdrawalCandidatureNominationDTO dto = new WithdrawalCandidatureNominationDTO();
			dto.setAkwcandidateSigned(akwcandidateSigned);
			dto.setAkwbywhomeformSubmit(akwbywhomeformSubmit);
			dto.setDate(dateField);
			dto.setTimeField(timeField);
			int result = withdrawalCandidatureNominationService.insertreportAKWdata(dto, userId, slno.toString());
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess", " ReportAcknowlwdged Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed ReportAcknowlwdged Data ...!!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ResponseEntity.ok("Success");
	}

	@RequestMapping(value = "/fetchFormAKWInfo")
	public String fetchFormAKWInfo(HttpServletRequest request,Model model, @RequestParam("slno") String slno) {
		try {
			String akwcandidateName = withdrawalCandidatureNominationService.akwName(slno);
			model.addAttribute("akwcandidateName", akwcandidateName);

			String akwcandidateDate = withdrawalCandidatureNominationService.akwDate(slno);
			model.addAttribute("akwcandidateDate", akwcandidateDate);

			String akwcandidateTime = withdrawalCandidatureNominationService.akwTime(slno);
			model.addAttribute("akwcandidateTime", akwcandidateTime);
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			model.addAttribute("gpNameTel",userRepo.getGpNameTel(user.getDistrict_id(), user.getRevenueDivisionId(),
					user.getMandalId(), user.getGpcode()));
			
			
			String akwbywhomeformSubmit = withdrawalCandidatureNominationService.akwbywhomeformSubmit(slno);
			model.addAttribute("akwbywhomeformSubmit", akwbywhomeformSubmit);
            String whomSubmitted="";
			if(akwbywhomeformSubmit.equals("1")) {
				whomSubmitted=	akwcandidateName;
			}
			if(akwbywhomeformSubmit.equals("2")) {
				whomSubmitted=userRepo.whomSubmitted_proposer(slno);
			}
			if(akwbywhomeformSubmit.equals("3")) {
				whomSubmitted=userRepo.whomSubmitted_election_agent(user.getDistrict_id(),user.getMandalId(), user.getGpcode());
			}
			model.addAttribute("whomSubmitted",whomSubmitted);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchFormReportAcknowledgeTel";
	}
	
	@RequestMapping(value = "/getWithdrawlConfirmCountAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlConfirmCountAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "mandalId") String mandalId, @RequestParam(value = "grampanchayatId") String grampanchayatId) {
		int count = 0;
		try { 
			count = withdrawalCandidatureNominationRepository.getWithdrawlConfirmCountAjax(districtId, revenueDivisionId, mandalId, grampanchayatId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	@RequestMapping(value = "/getWithdrawlConfirmByROAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlConfirmByROAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "mandalId") String mandalId, @RequestParam(value = "grampanchayatId") String grampanchayatId, 
			@RequestParam(value = "processComplete") int processComplete) {
		int count = 0;
		try {
				if(processComplete == 1) {
					count = withdrawalCandidatureNominationRepository.getWithdrawlConfirmByROAjax(districtId, revenueDivisionId, mandalId, grampanchayatId, processComplete);
				}else if(processComplete == 2){
					int processCount = withdrawalCandidatureNominationRepository.getWithdrawlProcessCount(districtId, revenueDivisionId, mandalId, grampanchayatId);
					if(processCount > 0) {
						int updateCount = withdrawalCandidatureNominationRepository.getWithdrawlConfirmByROAjax(districtId, revenueDivisionId, mandalId, grampanchayatId, processComplete);
						if(updateCount > 0) {
							count = withdrawalCandidatureNominationRepository.getWithdrawlDraftCount(districtId, revenueDivisionId, mandalId, grampanchayatId);
						}	
					}
				}else if(processComplete == 3){
					int draftCount =  withdrawalCandidatureNominationRepository.getWithdrawlDraftCount(districtId, revenueDivisionId, mandalId, grampanchayatId);
					if(draftCount > 0) {
						count = withdrawalCandidatureNominationRepository.getWithdrawlConfirmByROAjax(districtId, revenueDivisionId, mandalId, grampanchayatId, processComplete);
					}
				}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	
}