package cgg.gov.in.apsec.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyForMunicipalEntity;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.ULBOrdinaryRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyForMunicipalRepository;
import cgg.gov.in.apsec.repo.VacancyForNagarpanchayatsRepository;
import cgg.gov.in.apsec.service.AddGPWardService;
import cgg.gov.in.apsec.service.ElectionPollingCountingAgentsService;
import cgg.gov.in.apsec.service.UrbanRITBPOTPSScrutinyService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyForMunicipalController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private AddGPWardService gpWardSevice;

	@Autowired
	private VacancyForMunicipalRepository vacancyRepo;

	@Autowired
	private VacancyForNagarpanchayatsRepository vacancyRep;

	@Autowired
	private DistrictRepo districtRepo;
	
	@Autowired
	UrbanRITBPOTPSScrutinyService urbanRITBPOTPSScrutinyService;
	
	@Autowired
	private ElectionPollingCountingAgentsService electionPollingCountingAgentsService;
	@Autowired
	private ULBOrdinaryRepository ulbOrdinaryRepo;
	@GetMapping(value = "/vacancyForMunicipalCorporations")
	public String vacancyForMunicipalCorporations(Model model,
			@ModelAttribute("vacancyForMunicipalEntity") VacancyForMunicipalEntity vacancyForMunicipalEntity,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			List<Map<String, Object>> data = new ArrayList<>();

			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);

//				List<Map<String, Object>> assemblyList = userRepo.assemblyList(districtId);
				List<Map<String, Object>> assembliyList = ulbOrdinaryRepo.getAssemblyListByULBCode(detailsByUserId.getUlgCode(),
						detailsByUserId.getDistrict_id());
				model.addAttribute("assemblyList", assembliyList);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
//			List<Map<String, Object>> getULGWardCount = userRepo
//					.getULGWardCountByDist(detailsByUserId.getDistrict_id());
//			model.addAttribute("ulgWardsNo", getULGWardCount);
			
			List<Map<String, Object>> postDataForElectionProcess = electionPollingCountingAgentsService
					.getPostDataForElectionProcess();
			model.addAttribute("electionPosts", postDataForElectionProcess);
			
			if (detailsByUserId.getUlgCode() != null) {
				String ulgType = userRepo.getUlgTypeForVacancies(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode());
				Map<String, Object> ulgData = userRepo.getUlgNoOfWardsForVacancies(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode(), ulgType);
				model.addAttribute("ulbName", ulgData.get("ulb_name"));
				//model.addAttribute("noOfWards", ulgData.get("no_of_wards"));
				model.addAttribute("ulbType", ulgType);
				model.addAttribute("ulgCode", ulgData.get("ulb_code"));	
				
				Integer getULGWardCount = userRepo
						.getUlgWards(detailsByUserId.getDistrict_id(), ulgType,detailsByUserId.getUlgCode());
				model.addAttribute("ulgWardsNo", getULGWardCount);
			}
			model.addAttribute("vacancyForMunicipalEntity",vacancyForMunicipalEntity);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMunicipalCorporations";
	}

	@PostMapping(value = "/vacancyForMunicipalCorporationsSave")
	public String vacancyForMunicipalCorporationsSave(Model model,
			@ModelAttribute("vacancyForMunicipalEntity") VacancyForMunicipalEntity vacancyForMunicipalEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

 
			vacancyForMunicipalEntity.setUpdatedBy(userId);
			vacancyForMunicipalEntity.setSubmittedByUser(userId);

			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForMunicipalEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;

			if (vacancyForMunicipalEntity.getVacancyFillDatee() != null
					&& !vacancyForMunicipalEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForMunicipalEntity.getVacancyFillDatee());
			}

			vacancyForMunicipalEntity.setDateOfOccurrence(parsedDate);
			if (dateOfFilling != null) {
				vacancyForMunicipalEntity.setVacancyFillDate(dateOfFilling);
			}

			vacancyRepo.save(vacancyForMunicipalEntity);
			redirectAttr.addFlashAttribute("msg", "Vacancy for Municipal Corporations Added Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof AccessDeniedException) {
				throw (AccessDeniedException) e;
			}
			redirectAttr.addFlashAttribute("msgfail", "Failed to Add  ! Please try again....");
		}
		return "redirect:/vacancyForMunicipalCorporations";
	}

	@GetMapping(value = "/getVacancyForMunicipalCorporations")
	public String getVacancyForMunicipalCorporations(Model model,
			@ModelAttribute("vacancyForMunicipalEntity") VacancyForMunicipalEntity vacancyForMunicipalEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			List<Map<String, Object>> VacancyForMunicipal = vacancyRepo.getVacancyDetailsByDistrict(userId);
			model.addAttribute("VacancyForMunicipal", VacancyForMunicipal);

			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMunicipalList";
	}

	@PostMapping(value = "/vacancyForMunicipalCorporationsUpdateForm")
	public String vacancyForMunicipalCorporationsUpdateForm(Model model,HttpServletRequest request,
			@ModelAttribute("vacancyForMunicipalEntity") VacancyForMunicipalEntity vacancyForMunicipalEntity
			 ) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			int id = vacancyForMunicipalEntity.getId();
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);
			String ulgType = userRepo.getUlgTypeForVacancies(detailsByUserId.getDistrict_id(), detailsByUserId.getUlgCode());
			model.addAttribute("ulbType", ulgType);
			
//			List<Map<String, Object>> assemblyList = userRepo.assemblyList(detailsByUserId.getDistrict_id());
			List<Map<String, Object>> assembliyList = ulbOrdinaryRepo.getAssemblyListByULBCode(detailsByUserId.getUlgCode(),
					detailsByUserId.getDistrict_id());
			model.addAttribute("assemblyList", assembliyList);
			
//			List<Map<String, Object>> getULGWardCount = userRepo
//					.getULGWardCountByDist(detailsByUserId.getDistrict_id());
//			model.addAttribute("ulgWardsNo", getULGWardCount);
			
			Integer getULGWardCount = userRepo
					.getUlgWards(detailsByUserId.getDistrict_id(), ulgType,detailsByUserId.getUlgCode());
			model.addAttribute("ulgWardsNo", getULGWardCount);
			
			
			List<Map<String, Object>> data = new ArrayList<>();

			List<Map<String, Object>> VacancyForMunicipalUpdate = vacancyRepo.getVacancyUpdateForm(id);
			if (!VacancyForMunicipalUpdate.isEmpty()) {
				Map<String, Object> firstRow = VacancyForMunicipalUpdate.get(0);
				String districtid = (String) firstRow.get("district_id");
				vacancyForMunicipalEntity.setDistrictId(districtid);
				model.addAttribute("districtId", districtid);

				String distName = userRepo.getDistrictName(districtid);
				model.addAttribute("districtName", distName);

				String assemblyId = (String) firstRow.get("assembly_id");
				vacancyForMunicipalEntity.setAssemblyId(assemblyId);
				model.addAttribute("assembly_id", assemblyId);

				String assemblyName = vacancyRep.getAssemblyConstituencyName(districtid, assemblyId);
				model.addAttribute("assemblyName", assemblyName);

				String wardNo = (String) firstRow.get("ward_no");
				vacancyForMunicipalEntity.setWardNo(wardNo);
				model.addAttribute("ward_no", wardNo);

				String officeName = (String) firstRow.get("office_name");
				vacancyForMunicipalEntity.setOfficeName(officeName);
				model.addAttribute("office_name", officeName);

				String name = (String) firstRow.get("ulb_name");
				vacancyForMunicipalEntity.setUlbName(name);
				model.addAttribute("ulb_name", name);

				String reason = (String) firstRow.get("reason_vacancy");
				vacancyForMunicipalEntity.setReasonVacancy(reason);
				model.addAttribute("reason_vacancy", reason);

				String remarks = (String) firstRow.get("remarks");
				vacancyForMunicipalEntity.setRemarks(remarks);
				model.addAttribute("remarks", remarks);

				String any1 = (String) firstRow.get("any_vacancy");
				vacancyForMunicipalEntity.setAnyVacancy(any1);
				model.addAttribute("any_vacancy", any1);

				String anyImpediment = (String) firstRow.get("any_impediment");
				vacancyForMunicipalEntity.setAnyImpediment(anyImpediment);
				model.addAttribute("any_impediment", anyImpediment);

				String mayorWardId = (String) firstRow.get("mayor_ward_id");
				vacancyForMunicipalEntity.setMayorWardId(mayorWardId);
				model.addAttribute("mayorWardId", mayorWardId);
				
				String ulbType = (String) firstRow.get("ulb_type");
				vacancyForMunicipalEntity.setUlbType(ulbType);
				model.addAttribute("ulbType", ulbType);
				
				String ulbCode = (String) firstRow.get("ulb_code");
				vacancyForMunicipalEntity.setUlgCode(ulbCode);
				model.addAttribute("ulgCode", ulbCode);
				
				String electedName = (String) firstRow.get("elected_representative_name");
				vacancyForMunicipalEntity.setElectedRep(electedName);
				model.addAttribute("elected_representative_name", electedName);

				java.sql.Date sqlDate = (java.sql.Date) firstRow.get("date_of_occurrence");
				java.sql.Date sqlDate3 = (java.sql.Date) firstRow.get("vacancy_fill_date");

				Date utilDate = new Date(sqlDate.getTime());
				Date utilDate3 = null;
				if (sqlDate3 != null && !sqlDate3.toString().trim().isEmpty()) {
					utilDate3 = new Date(sqlDate3.getTime());
				}

				SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				String dateText = dateFormat.format(utilDate);
				String dateText3 = null;
				if (utilDate3 != null) {
					dateText3 = dateFormat.format(utilDate3);
				}

				vacancyForMunicipalEntity.setDateOfOccurrence(utilDate);
				if (utilDate3 != null) {
					vacancyForMunicipalEntity.setVacancyFillDate(utilDate3);
				}

				model.addAttribute("date_of_occurrence", dateText);
				if (dateText3 != null) {
					model.addAttribute("vacancy_fill_date", dateText3);
				}
				model.addAttribute("firstRow", firstRow);
			}
			
			
			model.addAttribute("populate", "populate");
			model.addAttribute("id", id);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyForMunicipalCorporations";
	}

	@Transactional
	@PostMapping(value = "/UpdateVacancyForMunicipalCorporations")
	public String UpdateVacancyForMunicipalCorporations(RedirectAttributes redirectAttr,
			@ModelAttribute("vacancyForMunicipalEntity") VacancyForMunicipalEntity vacancyForMunicipalEntity,
			HttpServletRequest request, @RequestParam(name = "id", required = false) Integer id) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");

			String districtId = vacancyForMunicipalEntity.getDistrictId();
			String officeName = vacancyForMunicipalEntity.getOfficeName();
			String assemblyId = vacancyForMunicipalEntity.getAssemblyId();
			String reason = vacancyForMunicipalEntity.getReasonVacancy();
			String remarks = vacancyForMunicipalEntity.getRemarks();
			String eleRep = vacancyForMunicipalEntity.getElectedRep();
 
						
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
			Date parsedDate = dateFormat.parse(vacancyForMunicipalEntity.getDateOfOccurrencee());
			Date dateOfFilling = null;

			if (vacancyForMunicipalEntity.getVacancyFillDatee() != null
					&& !vacancyForMunicipalEntity.getVacancyFillDatee().isEmpty()) {
				dateOfFilling = dateFormat.parse(vacancyForMunicipalEntity.getVacancyFillDatee());
			}

			if (dateOfFilling != null) {
				vacancyRepo.updateVacancyForMunicipalForFillingData(districtId, officeName, assemblyId,
						vacancyForMunicipalEntity.getWardNo(), vacancyForMunicipalEntity.getUlbName(), reason,
						parsedDate, remarks, vacancyForMunicipalEntity.getAnyVacancy(), userId,
						vacancyForMunicipalEntity.getAnyImpediment(), vacancyForMunicipalEntity.getMayorWardId(),
						eleRep,userId, id);
			} else {
				vacancyRepo.updateVacancyForMunicipal(districtId, officeName, assemblyId,
						vacancyForMunicipalEntity.getWardNo(), vacancyForMunicipalEntity.getUlbName(), reason,
						parsedDate, remarks, vacancyForMunicipalEntity.getAnyVacancy(), userId,
						vacancyForMunicipalEntity.getAnyImpediment(), vacancyForMunicipalEntity.getMayorWardId(),
						eleRep,userId, id);

			}

			vacancyForMunicipalEntity.setDateOfOccurrence(parsedDate);
			vacancyForMunicipalEntity.setVacancyFillDate(dateOfFilling);
			redirectAttr.addFlashAttribute("msg", "Vacancy for Municipal Corporations Updated Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			if (e instanceof AccessDeniedException) {
				throw (AccessDeniedException) e;
			}
			redirectAttr.addFlashAttribute("msgfail", "Failed to Update  ! Please try again....");
		}

		return "redirect:/getVacancyForMunicipalCorporations";
	}

	@Transactional
	@PostMapping(value = "/deleteVacancyForMunicipalCorporations")
	public String deleteVacancyForMunicipalCorporations(RedirectAttributes redirectAttr,
			@ModelAttribute("vacancyForMunicipalEntity") VacancyForMunicipalEntity vacancyForMunicipalEntity
			 ) {
		try {
			int id = vacancyForMunicipalEntity.getId();
			vacancyRepo.deleteByIdNative(id);
			redirectAttr.addFlashAttribute("msg", "Vacancy for Municipal Corporations Deleted Successfully....");
		} catch (Exception e) {
			e.printStackTrace();
			redirectAttr.addFlashAttribute("msgfail", "Failed to Delete  ! Please try again....");
		}
		return "redirect:/getVacancyForMunicipalCorporations";
	}
}