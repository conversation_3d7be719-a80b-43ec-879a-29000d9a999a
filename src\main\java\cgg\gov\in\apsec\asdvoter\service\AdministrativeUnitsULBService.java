package cgg.gov.in.apsec.asdvoter.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.itextpdf.text.pdf.PdfStructTreeController.returnType;

import cgg.gov.in.apsec.modal.AdministrativeUnitsUlbEntity;
import cgg.gov.in.apsec.pojo.UrbanLocalGovernmentElectionProcessDTO;
import cgg.gov.in.apsec.repo.AdministrativeUnitsULBRepositoy;

@Service
public class AdministrativeUnitsULBService {

	@Autowired
	private AdministrativeUnitsULBRepositoy repo;

	public int checkAdmUnitsExisted(UrbanLocalGovernmentElectionProcessDTO dto) {
		return repo.checkAdmUnitsExisted(dto.getDistrictId(), dto.getUlgCode());
	}

	public int saveUlb(UrbanLocalGovernmentElectionProcessDTO dto, String remoteAddr, String userId) {
		int c = 0;
		List<String> ulbcodes = dto.getUlbcodes();
		for (int i = 0; i < ulbcodes.size(); i++) {
			dto.setUlgCode(dto.getUlbcodes().get(i));
			int c1 = checkAdmUnitsExisted(dto);
			if (c1 == 0) {
				AdministrativeUnitsUlbEntity entity = new AdministrativeUnitsUlbEntity();
				
				if (dto.getUlbNames().get(i) == null ||
					    !dto.getUlbNames().get(i).matches("^[A-Za-z0-9\\-\\s]+$")) {
					    throw new IllegalArgumentException("ULB name must contain only letters, numbers, hyphens, and spaces.");
					}

				String rdNameTelugu = dto.getUlbNameTel().get(i);

		        if (rdNameTelugu != null) {
		            // Remove invisible characters like Zero-Width Space, Non-breaking Space
		            rdNameTelugu = rdNameTelugu.trim().replaceAll("[\u200B\u00A0]", "");
		            dto.getUlbNameTel().set(i, rdNameTelugu); // update cleaned value
		        }

		        if (rdNameTelugu == null ||
		            !rdNameTelugu.matches("^[\u0C00-\u0C7F\u200C\u200D\\s]+$")) {
		            throw new IllegalArgumentException("ULB Telugu name must contain only Telugu letters, without numbers or English.");
		        }

		        
				entity.setDistrictId(dto.getDistrictId());
				entity.setRevenueDivisionId(dto.getRevenueDivisionId());
				entity.setUlbCode(dto.getUlbcodes().get(i));
				entity.setUlbName(dto.getUlbNames().get(i));
				entity.setUlbType(dto.getUlbTypes().get(i));
				entity.setUlbNameTelugu(dto.getUlbNameTel().get(i));
				entity.setInsertedBy(userId);
				entity.setInsertedIpAddress(remoteAddr);
				AdministrativeUnitsUlbEntity save = repo.save(entity);
				if (save != null) {
					c += 1;
				}
			}
		}
		return c;
	}

	public Map<String, Object> getVmcPrintNotification(int slnoInt) {
		return repo.getVmcPrintNotification(slnoInt);
	}

	public List<Map<String, Object>> getReportOfAdministrativeUnitsUlb() {
		return repo.getReportOfAdministrativeUnitsUlb();
	}

//	public int deleteAdmUnitsUlb(UrbanLocalGovernmentElectionProcessDTO dto) {
//		AdministrativeUnitsUlbEntity en = repo.findById(Long.parseLong(dto.getSlno())).get();
//		if(repo.isExistsUrbanlUlg(en.getDistrictId(),en.getUlbCode())) {
//		return repo.deleteAdmUnitsUlb(Long.parseLong(dto.getSlno()));
//		}
//		else {
//			return 0;
//		}
//	}

	public int deleteAdmUnitsUlb(UrbanLocalGovernmentElectionProcessDTO dto) {
		int checkAdmUnitsExistedForULBNew = repo.checkAdmUnitsExistedForULBNew(Integer.parseInt(dto.getSlno()));
		if(checkAdmUnitsExistedForULBNew>0) {
		AdministrativeUnitsUlbEntity en = repo.findById(Long.parseLong(dto.getSlno())).get();
		return repo.deleteAdmUnitsUlb(Long.parseLong(dto.getSlno()));
		}
		else {
			return 0;
		}
	}

	public Map<String, Object> getAdministrativeUnitsForEdit(Integer slno) {
		return repo.getAdministrativeUnitsForEdit(slno);

	}

	public int updateAdministrativeUnit(UrbanLocalGovernmentElectionProcessDTO dto, Integer slno) {
		int updateCount = 0;
		if (dto.getUlgName() == null ||!dto.getUlgName().matches("^[A-Za-z0-9\\-\\s]+$")) {
						throw new IllegalArgumentException("ULB name must contain only letters, numbers, hyphens, and spaces.");
					}
			else if (dto.getUlbNameTelugu() == null || !dto.getUlbNameTelugu().trim().replaceAll("[\u200B\u00A0]", "").matches("^[\\u0C00-\\u0C7F\\u200C\\u200D\\s]+$")) {
							throw new IllegalArgumentException("ULB Telugu name must contain only Telugu letters, hyphens and spaces.");
					}
					dto.setUlbNameTelugu(dto.getUlbNameTelugu().trim().replaceAll("[\u200B\u00A0]", ""));
		try {

			int existingCount = checkAdmUnitsExistedForULBNew(dto,slno);
			if (existingCount > 0) {
				updateCount = repo.updateAdministrativeUnit(dto.getUlgCode(), dto.getUlgType(), dto.getUlgName(),
						dto.getUlbNameTelugu(), slno);
				// return -1;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return updateCount;
	}

	public List<Map<String, Object>> getDistrictUlbReportOfAdministrativeUnitsUlb(String districtId, String ulgType) {
		return repo.getDistrictUlbReportOfAdministrativeUnitsUlb(districtId, ulgType);
	}

	public int checkAdmUnitsExistedForULB(UrbanLocalGovernmentElectionProcessDTO dto) {
		return repo.checkAdmUnitsExistedForULB(dto.getUlgCode());
	}
	
	public int checkAdmUnitsExistedForULBNew(UrbanLocalGovernmentElectionProcessDTO dto,Integer slno) {
//		return repo.checkAdmUnitsExistedForULB(dto.getUlgCode(),slno);
		return repo.checkAdmUnitsExistedForULBNew(slno);
	}
}
