package cgg.gov.in.apsec.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.PostalBallotRuralDTO;
import cgg.gov.in.apsec.pojo.ReservationForZptcRoDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.PostalBallotService;
import cgg.gov.in.apsec.service.ReservationForZptcROService;
import cgg.gov.in.apsec.utils.FileUploadDownloadPaths;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class ZptcPostalBallotController {

	@Autowired
	private ReservationForZptcROService reservationForZptcROService;

	@Autowired
	private PostalBallotService postalBallotService;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private FileUploadDownloadPaths fileUploadDownloadPaths;

	@GetMapping("/getZptcPostalBallot")
	public String getZptcPostalBallot(
			@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO reservationForZptcRoDTO, Model model,
			HttpServletRequest request) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			model.addAttribute("username", userId);
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);

			List<Map<String, Object>> data = userRepo.getElectoralRollNotificationDataMptc();
			model.addAttribute("electionDesc", data);

			List<Map<String, Object>> electionDuties = postalBallotService.getElectionDuties();
			model.addAttribute("electionDuties", electionDuties);

			model.addAttribute("slbtype", "Rural Local Bodies");
		} catch (Exception e) {

			e.printStackTrace();
		}

		return "zptcPostalBallot";
	}

	@PostMapping("/savePostalBallotApplication")
	public String savePostalBallotApplication(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO dto,
			Model model, HttpServletRequest request, RedirectAttributes rdAttr) {

		try {
			int saveCount = reservationForZptcROService.savePostalBallotApplication(dto, request);
			if (saveCount > 0) {

				rdAttr.addFlashAttribute("saveSuccess", "Your Postal Ballot applied Successfully..!!");
			} else {

				rdAttr.addFlashAttribute("saveFailure", "Failed to apply Your Postal Ballot application..!!");
			}
		} catch (Exception e) {

			e.printStackTrace();
		}

		return "redirect:/getZptcPostalBallot";
	}

	@PostMapping("/getPostalBallotZptcReport")
	public String getPostalBallotZptcReport(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO dto,
			Model model, HttpServletRequest request) {

		try {

			String userId = request.getSession().getAttribute("loggedInUser").toString();
			List<Map<String, Object>> postalBallotReportForZptc = reservationForZptcROService
					.getPostalBallotReportForZptc(userId);
			model.addAttribute("postalBallotReportForZptc", postalBallotReportForZptc);
		} catch (Exception e) {

			e.printStackTrace();
		}
		return "zptcPostalBllotReport";
	}

	@GetMapping("/getZptcPostalBallotApproval")
	public String getZptcPostalBallotApproval(@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO dto,
			Model model, HttpServletRequest request) {

		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String zppCode = user.getZppCode();
			System.out.println("zppCode " + zppCode);
			Map<String, Object> zppData = reservationForZptcROService.getZppForROLogin(zppCode);
			model.addAttribute("zppCode", zppData.get("zppCode"));
			List<Map<String, Object>> data = userRepo.getElectoralRollNotificationDataMptc();
			model.addAttribute("electionDesc", data);
		} catch (Exception e) {

			e.printStackTrace();
		}

		return "zptcPostalBallotApproval";
	}

	@GetMapping("/getZptcPostalBallotDataForApproval")
	@ResponseBody
	public List<Map<String, Object>> getZptcPostalBallotDataForApproval(
			@RequestParam("electionCode") String electionCode, @RequestParam("zppCode") String zppCode) {
		List<Map<String, Object>> dashboardData = new ArrayList<>();
		try {

			List<Integer> districtIds = reservationForZptcROService.findDistrictIdByZppCode(Integer.parseInt(zppCode));

			dashboardData = reservationForZptcROService.getZptcPostalBallotApplicationsSummary(districtIds,
					electionCode);
		} catch (Exception e) {

			e.printStackTrace();
		}

		return dashboardData;
	}

	@PostMapping("/processApplicationPostalBallotZptc")
	public String processApplicationPostalBallotZptc(
			@ModelAttribute("reservationForZptcRoDTO") ReservationForZptcRoDTO dto, Model model,
			HttpServletRequest request, @RequestParam(value = "type") String type,
			@RequestParam(value = "electionCode") String electionCode) {

		try {

			String userId = request.getSession().getAttribute("loggedInUser").toString();

			String homePath = fileUploadDownloadPaths.getHomedir();

			User user = userRepo.getDetailsByUserId(userId);
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String zppCode = user.getZppCode();

			List<Integer> statusIds = new ArrayList<>();
			System.out.println("type is " + type);
			if ("total".equals(type)) {
				statusIds = Arrays.asList(1, 2, 3);
			} else if ("pending".equals(type)) {
				statusIds = Collections.singletonList(1);
			} else if ("accepted".equals(type)) {
				statusIds = Collections.singletonList(2);
			} else if ("rejected".equals(type)) {
				statusIds = Collections.singletonList(3);
			}
			List<Integer> districtIds = reservationForZptcROService.findDistrictIdByZppCode(Integer.parseInt(zppCode));
			System.out.println("districtIds " + districtIds);
			List<Map<String, Object>> applications = reservationForZptcROService
					.getFilteredApplicationsData(districtIds, electionCode, statusIds);

			model.addAttribute("applications", applications);
			if (!applications.isEmpty()) {
				model.addAttribute("status_id", applications.get(0).get("status_id"));
			}
			model.addAttribute("no_of_appl", applications.size());
			model.addAttribute("rejectReasonsList", userRepo.getRejectedReasons());
		} catch (Exception e) {

			e.printStackTrace();
		}

		return "zptcFilteredApplicationsReport";
	}

	@PostMapping("/processForZptcPb")
	public String processApplications(@ModelAttribute("postalBallotRuralDTO") PostalBallotRuralDTO postalBallotRuralDTO,
			Model model, HttpServletRequest request, RedirectAttributes redirectAttr) {
		int app_sno = 0, status_id = 0, appCount = 0, reject_reason_id = 0, cssFlag = 0;
		String ipAddress = request.getRemoteAddr();
		String disp_msg = null;
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);

			String zppCode = user.getZppCode();
			List<Integer> districtIds = reservationForZptcROService.findDistrictIdByZppCode(Integer.parseInt(zppCode));

			List<Map<String, Object>> appData = reservationForZptcROService
					.findApplicationSnoByStatusAndDistrict(districtIds);

			for (Map<String, Object> m : appData) {
				app_sno = (int) m.get("appSno");
				status_id = Integer.parseInt(request.getParameter("status_id_" + app_sno));
				reject_reason_id = (status_id == 3) ? Integer.parseInt(request.getParameter("reason_id_" + app_sno))
						: 0;
				if (status_id != 0) {
					appCount++;
					reservationForZptcROService.saveApplicationStatusForZptcs(app_sno, status_id, userId, ipAddress);
					reservationForZptcROService.updateStatusAndRejectReasonForZptcPB(status_id, reject_reason_id,
							app_sno);

				}
			}

			if (appCount == appData.size()) {
				disp_msg = "Applications processed successfully.";
				cssFlag = 1;
			} else {
				disp_msg = "Error processing applications.";
			}

		} catch (Exception e) {
			e.printStackTrace();
			disp_msg = "An error occurred.";
		}
		redirectAttr.addFlashAttribute("message", disp_msg);
		redirectAttr.addFlashAttribute("cssFlag", cssFlag);
		return "redirect:/getZptcPostalBallotApproval";
	}

	@RequestMapping(value = "/fetchForm14ZptcData")
	public String fetchForm14RuralData(@RequestParam("app_sno") String app_sno, Model model,
			HttpServletRequest request) {
		try {

			Map<String, Object> distEpicData = reservationForZptcROService
					.findResidentDistrictIdAndEpicIdBySno(Integer.parseInt(app_sno));
			Map<String, Object> pbDataForFormZptc = reservationForZptcROService.getPbDataForFormZptc(
					distEpicData.get("epic_id").toString(), distEpicData.get("resident_district_id").toString());
			model.addAttribute("pbPdf", pbDataForFormZptc);
			String gpNameTel = reservationForZptcROService.getGpNameTel(
					Integer.parseInt(distEpicData.get("district_id").toString()),
					Integer.parseInt(distEpicData.get("mandal_id").toString()),
					Integer.parseInt(distEpicData.get("gpcode").toString()));
			model.addAttribute("ward_id", distEpicData.get("ward_id").toString());
			model.addAttribute("gpNameTel", gpNameTel);
			String districtNameTel = userRepo
					.getDistrictNameTelInt(Integer.parseInt(distEpicData.get("district_id").toString()));
			model.addAttribute("districtNameTel", districtNameTel);
			Map<String, Object> zptcName = reservationForZptcROService.getMandalTelugu(
					distEpicData.get("mandal_id").toString(), distEpicData.get("district_id").toString());
			model.addAttribute("zptcName", zptcName);
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);

			String zppCode = user.getZppCode();

			Map<String, Object> zppData = reservationForZptcROService.getZppForROLogin(zppCode);
			model.addAttribute("zppName", zppData.get("zppName"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "formFourteenForZptcPdf";
	}
}
