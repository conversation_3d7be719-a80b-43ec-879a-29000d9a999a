package cgg.gov.in.apsec.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.DirectVacancySarpanchEntity;
import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyCPRSarpanchDTO;
import cgg.gov.in.apsec.repo.AddMandalRepository;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.MasterMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyDLPOSarpanchConfirmCustomRepository;
import cgg.gov.in.apsec.repo.VacancyDPOSarpanchConfirmationRepository;
import cgg.gov.in.apsec.service.AddGPWardService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class VacancyDPOSarpanchConfirmationController {

	@Autowired
	private AddMandalRepository addMandalRepository;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private AddGPWardService gpWardSevice;

	@Autowired
	private MasterMandalRepository mastermandalrepo;

	@Autowired
	private VacancyDPOSarpanchConfirmationRepository vacDPOConfirmRepo;

	@Autowired
	private VacancyDLPOSarpanchConfirmCustomRepository customRepo;

	@Autowired
	private DistrictRepo districtRepo;

	@GetMapping(value = "/vacancyDPOSarpanchConfirmation")
	public String vacancyDPOSarpanchConfirmation(Model model,
			@ModelAttribute("DirectVacancySarpanchEntity") DirectVacancySarpanchEntity directVacancySarpanchEntity,
			HttpServletRequest request) {
		try {
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();

			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);
				List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
				model.addAttribute("revenueDiv", revenueDiv);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDPOSarpanchConfirmation";
	}

	/*@RequestMapping(value = "/getVacancyDPOSarpanchConfirmdtls", method = RequestMethod.POST)
	public String getVacancyDPOSarpanchConfirmdtls(Model model,
			@ModelAttribute("DirectVacancySarpanchEntity") DirectVacancySarpanchEntity directVacancySarpanchEntity,
			@RequestParam(name = "districtId", required = false) String districtId,
			@RequestParam(name = "revenueDivisionId", required = false) String revenueDivisionId,
			@RequestParam(name = "assemblyId", required = false) String assemblyId,
			@RequestParam(name = "mandalId", required = false) String mandalId) {*/
	
	@PostMapping("getVacancyDPOSarpanchConfirmdtls")
	public String getVacancyDPOSarpanchConfirmdtls(
			@ModelAttribute("DirectVacancySarpanchEntity") DirectVacancySarpanchEntity directVacancySarpanchEntity,
			RedirectAttributes redirectAttr, HttpServletRequest request,Model model) {
		String token = TokenUtil.generateToken(request.getSession());
		model.addAttribute("formToken", token);
		String mandalId = directVacancySarpanchEntity.getMandalIdNew();
		String districtId = directVacancySarpanchEntity.getDistrictIdNew();
		String revenueDivisionId = directVacancySarpanchEntity.getRevenueDivisionIdNew();
		
		List<Map<String, Object>> vacDPOSarDtls = null;
		
			vacDPOSarDtls = customRepo.findVacancySarpanch(districtId,
					revenueDivisionId,mandalId);
			model.addAttribute("vacDPOSarDtls",vacDPOSarDtls);
			if (vacDPOSarDtls.size() == 0) {
				model.addAttribute("failMessage", "Data Not Found");
			}
 

		if (districtId != null && !"0".equals(districtId)) {
			District district = districtRepo.findById(districtId).orElse(null);
			Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
			model.addAttribute("districtData", districtData);
			model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
			model.addAttribute("districtId", districtId);
			List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
			model.addAttribute("revenueDiv", revenueDiv);
			List<Map<String, Object>> assemblyConstituencyList = userRepo.assemblyList(districtId);
			model.addAttribute("assemblyConstituencyList", assemblyConstituencyList);
		} else {
			List<Map<String, Object>> districtData = userRepo.findDistricts();
			model.addAttribute("districtData", districtData);
		}
		List<Map<String, Object>> listOfMandals =new  ArrayList<>();
		if ("all".equalsIgnoreCase(revenueDivisionId)) {
	        // Fetch all mandals for the district
			listOfMandals = addMandalRepository.getAllMandalsByDistrict(Integer.parseInt(districtId));
	    } else {
	    	
	   
		 listOfMandals = addMandalRepository
				.getMandalsByDistRevenueInt(Integer.parseInt(districtId), Integer.parseInt(revenueDivisionId));
	    }
		
		model.addAttribute("listOfMandals", listOfMandals);
		model.addAttribute("mandalIdSelected", mandalId);
		return "vacancyDPOSarpanchConfirmation";
	}

	@RequestMapping(value = "/vacancyDPOSarpanchConfirmation", method = RequestMethod.POST)
	public String confirmVacancy(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("assemblyId") String assemblyId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.saveVacancyDPOSarpanchConfirmation(id, districtId, revenueDivisionId, assemblyId,
				mandalId, gpcode, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details confirmed successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDPOSarpanchConfirmation";
	}

	@RequestMapping(value = "/vacancyDPOSarpanchRollback", method = RequestMethod.POST)
	public String vacancySarpanchRollback(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("assemblyId") String assemblyId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDPOSarpanchRollback(id, districtId, revenueDivisionId, assemblyId, mandalId,
				gpcode, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details Rolledback successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to Rolledback details.");
		}
		return "redirect:/vacancyDPOSarpanchConfirmation";
	}

	@GetMapping(value = "/getVacancyDPOSarpanchforView")
	public String getVacancyDPOSarpanchforView(Model model,
			@ModelAttribute("DirectVacancySarpanchEntity") DirectVacancySarpanchEntity directVacancySarpanchEntity) {
		try {
			List<Map<String, Object>> vacancyDPOCnfRoll = vacDPOConfirmRepo.getVacancyDPOSarpanchforView();
			model.addAttribute("vacancyDPOCnfRoll", vacancyDPOCnfRoll);

			model.addAttribute("now", new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDPOSarpanchConfirmRollbackList";
	}
	
	@RequestMapping(value = "/saveVacancyDPOSarpanchConfirmation", method = RequestMethod.POST)
	public String saveVacancyDLPOSarpanchConfirmation(
			@ModelAttribute("VacancyCPRSarpanchDTO") VacancyCPRSarpanchDTO vacancyCPRSarpanchDTO,
			RedirectAttributes redirectAttr) {
		////log.info("Enter into saveVacancyCPRSarpanchConfirmation method");
		try {
			boolean isSaved = customRepo.saveVacancyDPOSarpanchConfirmation(vacancyCPRSarpanchDTO.getSelectedItems());
			if (isSaved) {
				redirectAttr.addFlashAttribute("msg", "Vacancy confirmation saved successfully.");
			} else {
				redirectAttr.addFlashAttribute("msgfail", "Failed to save vacancy confirmation.");
			}
		} catch (Exception e) {
			//logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/vacancyDPOSarpanchConfirmation";
	}
	@RequestMapping(value = "/saveVacancyDPOSarpanchRollback", method = RequestMethod.POST)
	public String saveVacancyDLPOSarpanchRollback(
			@ModelAttribute("VacancyCPRSarpanchDTO") VacancyCPRSarpanchDTO vacancyCPRSarpanchDTO,
			RedirectAttributes redirectAttr) {
		////log.info("Enter into saveVacancyCPRSarpanchConfirmation method");
		try {
			boolean isSaved = customRepo.saveVacancyDPOSarpanchRolledback(vacancyCPRSarpanchDTO.getSelectedItems());
			if (isSaved) {
				redirectAttr.addFlashAttribute("msg", "Details Rolledback successfully.");
			} else {
				redirectAttr.addFlashAttribute("msgfail", "Failed to Rolledback details.");
			}
		} catch (Exception e) {
			//logger.error("Error at saveVacancyCPRSarpanchConfirmation method: " + e.getMessage(), e);
		}
		return "redirect:/vacancyDPOSarpanchConfirmation";
	}
}