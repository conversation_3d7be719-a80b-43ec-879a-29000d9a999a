package cgg.gov.in.apsec.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.District;
import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.modal.VacancyForchairpersonEntity;
import cgg.gov.in.apsec.repo.DistrictRepo;
import cgg.gov.in.apsec.repo.MasterMandalRepository;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.VacancyDLPOChairPersonConfirmCustomRepository;
import cgg.gov.in.apsec.repo.VacancyDPOChairPersonConfirmRepository;
import cgg.gov.in.apsec.service.AddGPWardService;

@Controller
public class VacancyDPOChairPersonConfirmController {

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private AddGPWardService gpWardSevice;

	@Autowired
	private MasterMandalRepository mastermandalrepo;

	@Autowired
	private VacancyDPOChairPersonConfirmRepository vacCPRepo;

	@Autowired
	private VacancyDLPOChairPersonConfirmCustomRepository customRepo;

	@Autowired
	private DistrictRepo districtRepo;

	@GetMapping(value = "/vacancyDPOChairPersonConfirm")
	public String vacancyDPOChairPersonConfirm(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("loggedInUser");
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			if (districtId != null && !"0".equals(districtId)) {
				District district = districtRepo.findById(districtId).orElse(null);
				Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
				model.addAttribute("districtData", districtData);
				model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
				model.addAttribute("districtId", districtId);
				List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
				model.addAttribute("revenueDiv", revenueDiv);
			} else {
				List<Map<String, Object>> districtData = userRepo.findDistricts();
				model.addAttribute("districtData", districtData);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDPOChairPersonConfirm";
	}

	@RequestMapping(value = "/getVacancyDPOChairperson", method = RequestMethod.POST)
	public String getVacancyDPOChairperson(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			@RequestParam(name = "districtId", required = false) String districtId,
			@RequestParam(name = "revenueDivisionId", required = false) String revenueDivisionId,
			@RequestParam(name = "gpcode", required = false) String gpcode) {

		List<Map<String, Object>> vacCPDPODtls = vacCPRepo.getVacancyDtlsDPOChairPerson(districtId, revenueDivisionId,
				gpcode);
		if (districtId != null && !"0".equals(districtId)) {
			District district = districtRepo.findById(districtId).orElse(null);
			Map<String, Object> districtData = gpWardSevice.findDistrict(districtId);
			model.addAttribute("districtData", districtData);
			model.addAttribute("districtName", district != null ? district.getDistrictName() : "");
			model.addAttribute("districtId", districtId);
			List<Map<String, Object>> revenueDiv = mastermandalrepo.findRevenueDivisionsIdByDistrictId(districtId);
			model.addAttribute("revenueDiv", revenueDiv);
		}
		model.addAttribute("vacCPDPODtls", vacCPDPODtls);
		if (vacCPDPODtls.size() == 0) {
			model.addAttribute("getMsg", "true");
		}
		return "vacancyDPOChairPersonConfirm";
	}

	@RequestMapping(value = "/vacancyDpoCpConfirm", method = RequestMethod.POST)
	public String vacancyDlpoCpConfirm(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("assemblyId") String assemblyId,
			@RequestParam("gpcode") String gpcode, @RequestParam("officeName") String officeName,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			@RequestParam("territorialConstituency") String territorialConstituency,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDpoCpConfirm(id, districtId, revenueDivisionId, assemblyId, gpcode,
				officeName, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate,
				territorialConstituency);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details confirmed successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDPOChairPersonConfirm";
	}

	@RequestMapping(value = "/vacancyDpoCpRollback", method = RequestMethod.POST)
	public String vacancyDlpoCpRollback(@RequestParam("id") int id, @RequestParam("districtId") String districtId,
			@RequestParam("revenueDivisionId") String revenueDivisionId, @RequestParam("assemblyId") String assemblyId,
			@RequestParam("gpcode") String gpcode, @RequestParam("officeName") String officeName,
			@RequestParam("dateOfOccurrence") String dateOfOccurrence,
			@RequestParam("reasonVacancy") String reasonVacancy, @RequestParam("anyImpediment") String anyImpediment,
			@RequestParam("remarks") String remarks, @RequestParam("vacancyFillDate") String vacancyFillDate,
			@RequestParam("territorialConstituency") String territorialConstituency,
			RedirectAttributes redirectAttributes) {
		boolean isSaved = customRepo.vacancyDpoCpRollback(id, districtId, revenueDivisionId, assemblyId, gpcode,
				officeName, dateOfOccurrence, reasonVacancy, anyImpediment, remarks, vacancyFillDate,
				territorialConstituency);
		if (isSaved) {
			redirectAttributes.addFlashAttribute("msg", "Details confirmed successfully.");
		} else {
			redirectAttributes.addFlashAttribute("msgfail", "Failed to confirm details.");
		}
		return "redirect:/vacancyDPOChairPersonConfirm";
	}

	@GetMapping(value = "/getVacancyDPOforCPView")
	public String getVacancyDPOforCPView(Model model,
			@ModelAttribute("vacancyForchairpersonEntity") VacancyForchairpersonEntity vacancyForchairpersonEntity,
			HttpServletRequest request, RedirectAttributes redirectAttr) {
		try {

			model.addAttribute("now", new Date());

			List<Map<String, Object>> VacancyDetailsCnfRoll = vacCPRepo.getVacancyDPOforCPforView();
			model.addAttribute("VacancyDetailsCnfRoll", VacancyDetailsCnfRoll);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "vacancyDPOChairPersonConfirmRollbackList";
	}
}