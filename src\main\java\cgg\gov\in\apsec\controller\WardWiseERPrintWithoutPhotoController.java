
package cgg.gov.in.apsec.controller;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.GPwiseVoterListRuralDTO;
import cgg.gov.in.apsec.pojo.PsWiseSarpanchElectoralRollWithoutphotoDTO;
import cgg.gov.in.apsec.repo.GpwiseRepoforMysql;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.service.GPWardwiseVoterListRuralService;
import cgg.gov.in.apsec.service.PsWiseSarpanchElectoralRollWithoutphotoService;
import cgg.gov.in.apsec.service.WardWiseERPrintWithoutPhotoService;

@Controller
public class WardWiseERPrintWithoutPhotoController {

	@Autowired
	private GPWardwiseVoterListRuralService gpWVLRuralService;

	@Autowired
	private PsWiseSarpanchElectoralRollWithoutphotoService psWiseSarpanchElectoralRollWithoutphotoService;

	@Autowired
	private GpwiseRepoforMysql gpwiseMsSqlRepo;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private WardWiseERPrintWithoutPhotoService wardWiseERPrintWithoutPhotoService;

	@GetMapping("/getWardWiseERPrintWithoutPhoto")
	public String getWardWiseERPrintWithoutPhoto(Model model, HttpServletRequest request,
			@ModelAttribute("psWiseSarpanchElectoralRollWithoutphotoDTO") PsWiseSarpanchElectoralRollWithoutphotoDTO psWiseSarpanchElectoralRollWithoutphotoDTO) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User detailsByUserId = userRepo.getDetailsByUserId(userId);
			String districtId = detailsByUserId.getDistrict_id();
			String mandalId = detailsByUserId.getMandalId();

			List<Map<String, Object>> elections = gpWVLRuralService.getElections();
			model.addAttribute("elections", elections);

			List<Map<String, Object>> gPs = psWiseSarpanchElectoralRollWithoutphotoService.getGPs(districtId, mandalId);
			model.addAttribute("gPs", gPs);

			Map<String, Object> district = psWiseSarpanchElectoralRollWithoutphotoService.getDistrict(districtId);
			model.addAttribute("district", district);

			Map<String, Object> mandal = psWiseSarpanchElectoralRollWithoutphotoService.getMandal(districtId, mandalId);
			model.addAttribute("mandal", mandal);
			model.addAttribute("dataNot", "Data");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "wardWiseERPrintWithoutPhoto";
	}

	@PostMapping("/getWardWiseERPrintWithoutPhoto")
	public String getWardWiseCountOfVoters(Model model, RedirectAttributes redirectAttr,
			@ModelAttribute("psWiseSarpanchElectoralRollWithoutphotoDTO") PsWiseSarpanchElectoralRollWithoutphotoDTO psWiseSarpanchElectoralRollWithoutphotoDTO) {
		try {
			String districtId = psWiseSarpanchElectoralRollWithoutphotoDTO.getDistrictId();
			String mandalId = psWiseSarpanchElectoralRollWithoutphotoDTO.getMandalId();
			String gpCode = psWiseSarpanchElectoralRollWithoutphotoDTO.getGpCode();

			List<Map<String, Object>> data = gpwiseMsSqlRepo.getGPWiseData(districtId, mandalId, gpCode);
			redirectAttr.addFlashAttribute("data", data);

			String districtNameInt = userRepo.getDistrictNameInt(Integer.parseInt(districtId));
			redirectAttr.addFlashAttribute("districtName", districtNameInt);

			Map<String, Object> mandalInt = psWiseSarpanchElectoralRollWithoutphotoService
					.mandalInt(Integer.parseInt(districtId), Integer.parseInt(mandalId));
			redirectAttr.addFlashAttribute("mandalInt", mandalInt);

			Map<String, Object> gpNameInt = wardWiseERPrintWithoutPhotoService
					.getGPNameInt(Integer.parseInt(districtId), Integer.parseInt(mandalId), Integer.parseInt(gpCode));
			redirectAttr.addFlashAttribute("gpNameInt", gpNameInt);

			redirectAttr.addFlashAttribute("districtId", districtId);
			redirectAttr.addFlashAttribute("districtName", districtNameInt);
			redirectAttr.addFlashAttribute("electionId", psWiseSarpanchElectoralRollWithoutphotoDTO.getElectionCode());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/getWardWiseERPrintWithoutPhoto";
	}

	@GetMapping("viewEnglishWardWiseVoters")
	public String viewEnglishWardWiseVoters(@RequestParam("districtId") String districtId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("electionId") String electionId, HttpServletRequest request, Model model) {
		int totalVoters = 0, maleVoter = 0, femaleVoter = 0, otherVoter = 0;
		String eleDetails = null, distName = null, mandal_name = null, gpname = null;
		Map<String, Object> m = new HashMap<>();
		try {
			int districtIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(districtId)));
			int mandalIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(mandalId)));
			int gpcodeInt = Integer.parseInt(new String(Base64.getDecoder().decode(gpcode)));
			int electionIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(electionId)));

			model.addAttribute("electionId", electionIdInt);
			model.addAttribute("districtId", districtIdInt);
			model.addAttribute("mandalId", mandalIdInt);
			model.addAttribute("gpCode", gpcodeInt);
			model.addAttribute("wardId", 1);
			if (electionId != null && !"".equals(electionId.trim())) {
				eleDetails = userRepo.getElectionHeadingInt(electionIdInt + "");
			} else {
				eleDetails = " GP WISE  ELECTORAL  ROLLS  LIST ";
			}
			String signature = gpWVLRuralService.getSignature(districtIdInt + "");
			if (null != signature && !("".equals(signature))) {
				model.addAttribute("signature", signature);
			}

			distName = gpWVLRuralService.getDistName(districtIdInt + "");
			if (mandal_name == null) {
				Map<String, Object> mandalInt = psWiseSarpanchElectoralRollWithoutphotoService.mandalInt(districtIdInt,
						mandalIdInt);
				mandal_name = mandalInt.get("mandal_name") + "";
			} else {
				mandal_name = "--";
			}
			if (gpcode != null) {
				gpname = wardWiseERPrintWithoutPhotoService.getGPNameInt(districtIdInt, mandalIdInt, gpcodeInt)
						.get("gpname") + "";
			} else {
				gpname = "--";
			}
			model.addAttribute("eleDetails", eleDetails);
			model.addAttribute("distName", distName);
			model.addAttribute("mandal_name", mandal_name);
			model.addAttribute("gpname", gpname);

			model.addAttribute("dateTime", "      ");
			List<Map<String, Object>> assemblyList = wardWiseERPrintWithoutPhotoService
					.getAssemblyDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("assemblyList", assemblyList);
			model.addAttribute("assemblyName", assemblyList.get(0).get("assembly_name"));
			List<Map<String, Object>> no_of_electors_data = wardWiseERPrintWithoutPhotoService
					.getGpWaiseData(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");

			List<Map<String, Object>> data = wardWiseERPrintWithoutPhotoService.getVoterListWardWise(districtIdInt + "",
					mandalIdInt + "", gpcodeInt + "");
			List<Map<String, Object>> mukapathramDet = wardWiseERPrintWithoutPhotoService
					.getIndexPage(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("no_of_electors_data", no_of_electors_data);
			model.addAttribute("mukpathramDet", mukapathramDet);
			String partNos = "";
			int prtCnts = 0;
			for (Map<String, Object> mo : mukapathramDet) {
				model.addAttribute("revDivName", (String) mo.get("revenue_division_name"));
				model.addAttribute("assemblyId", (String) mo.get("assembly_id"));
				if (partNos.length() > 0 && !partNos.contains((String) mo.get("partno"))) {
					partNos += "," + (String) mo.get("partno");
					prtCnts = prtCnts + 1;
				} else if (partNos.length() < 1) {
					partNos = (String) mo.get("partno");
					prtCnts = 1;
				}
			}

			model.addAttribute("partNos", partNos);
			model.addAttribute("prtCnts", prtCnts);
			Calendar now = Calendar.getInstance();
			int year = now.get(Calendar.YEAR);
			String electionYear = String.valueOf(year);
			model.addAttribute("electionYear", electionYear);
			List<String> photos = new ArrayList<String>();
			if (data != null && data.size() > 0) {
				for (int i = 0; i < data.size(); i++) {
					m = (Map) data.get(i);
					if (m.get("photo") != null && !"null".equals(m.get("photo") + "")) {
						byte[] photoBytes = (byte[]) m.get("photo");
						String photo = Base64.getEncoder().encodeToString(photoBytes);
						photos.add(photo);
					} else {
						photos.add("NPA");
					}
					if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("M") || (m.get("sex") + "").trim().equals("m"))) {
						maleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").equals("F") || (m.get("sex") + "").equals("f"))) {
						femaleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("O") || (m.get("sex") + "").trim().equals("o"))) {
						otherVoter += 1;
					}
				}

				totalVoters = data.size();
				model.addAttribute("photos", photos);
				model.addAttribute("totalVoters", totalVoters);
				model.addAttribute("maleVoters", maleVoter);
				model.addAttribute("femaleVoters", femaleVoter);
				model.addAttribute("otherVoters", otherVoter);

				model.addAttribute("data", data);
				model.addAttribute("dataForPart", data);

				String dtStr = wardWiseERPrintWithoutPhotoService.dtStrWardWise(districtIdInt + "", mandalIdInt + "",
						gpcodeInt + "");
				String ageAsOnOld = "", publicationDateOld = "", sup1_from_dt = "", sup1_to_dt = "";
				List<Map<String, Object>> electorsDataWardWise = wardWiseERPrintWithoutPhotoService
						.getElectorsDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
				model.addAttribute("electorsDataWardWise", electorsDataWardWise);
				if (!("0".equals(dtStr)) && !("").equals(dtStr)) {
					String temp[] = dtStr.split("##");
					ageAsOnOld = temp[0];
					publicationDateOld = temp[1];
					sup1_from_dt = temp[2];
					sup1_to_dt = temp[3];
				}

				model.addAttribute("age_as_on", ageAsOnOld);
				model.addAttribute("publication_date", publicationDateOld);
				model.addAttribute("sup1_from_dt", sup1_from_dt);
				model.addAttribute("sup1_to_dt", sup1_to_dt);

				int noOfPages = (int) Math.ceil(data.size() / 21.0);
				noOfPages = noOfPages + 1;

				DecimalFormat df = new DecimalFormat("#");
				String roundedPages = df.format(noOfPages);

				model.addAttribute("noOfPages", roundedPages);
			}
			GPwiseVoterListRuralDTO gpWVLRuralDto1 = new GPwiseVoterListRuralDTO();
			model.addAttribute("gpWVLRuralDto1", gpWVLRuralDto1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "gpWardWiseEnglishWithPhotoReport";
	}

	@GetMapping("viewEnglishWardWiseVotersWithCaste")
	public String viewEnglishWardWiseVotersWithCaste(@RequestParam("districtId") String districtId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("electionId") String electionId, HttpServletRequest request, Model model) {
		int totalVoters = 0, maleVoter = 0, femaleVoter = 0, otherVoter = 0;
		String eleDetails = null, distName = null, mandal_name = null, gpname = null;
		Map<String, Object> m = new HashMap<>();
		try {
			int districtIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(districtId)));
			int mandalIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(mandalId)));
			int gpcodeInt = Integer.parseInt(new String(Base64.getDecoder().decode(gpcode)));
			int electionIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(electionId)));

			model.addAttribute("electionId", electionIdInt);
			model.addAttribute("districtId", districtIdInt);
			model.addAttribute("mandalId", mandalIdInt);
			model.addAttribute("gpCode", gpcodeInt);
			model.addAttribute("wardId", 1);
			if (electionId != null && !"".equals(electionId.trim())) {
				eleDetails = userRepo.getElectionHeadingInt(electionIdInt + "");
			} else {
				eleDetails = " GP WISE  ELECTORAL  ROLLS  LIST ";
			}
			String signature = gpWVLRuralService.getSignature(districtIdInt + "");
			if (null != signature && !("".equals(signature))) {
				model.addAttribute("signature", signature);
			}

			distName = gpWVLRuralService.getDistName(districtIdInt + "");
			if (mandal_name == null) {
				Map<String, Object> mandalInt = psWiseSarpanchElectoralRollWithoutphotoService.mandalInt(districtIdInt,
						mandalIdInt);
				mandal_name = mandalInt.get("mandal_name") + "";
			} else {
				mandal_name = "--";
			}
			if (gpcode != null) {
				gpname = wardWiseERPrintWithoutPhotoService.getGPNameInt(districtIdInt, mandalIdInt, gpcodeInt)
						.get("gpname") + "";
			} else {
				gpname = "--";
			}
			model.addAttribute("eleDetails", eleDetails);
			model.addAttribute("distName", distName);
			model.addAttribute("mandal_name", mandal_name);
			model.addAttribute("gpname", gpname);

			model.addAttribute("dateTime", "      ");
			List<Map<String, Object>> assemblyList = wardWiseERPrintWithoutPhotoService
					.getAssemblyDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("assemblyList", assemblyList);
			model.addAttribute("assemblyName", assemblyList.get(0).get("assembly_name"));
			List<Map<String, Object>> no_of_electors_data = wardWiseERPrintWithoutPhotoService
					.getGpWaiseData(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");

			List<Map<String, Object>> data = wardWiseERPrintWithoutPhotoService.getVoterListWardWise(districtIdInt + "",
					mandalIdInt + "", gpcodeInt + "");
			List<Map<String, Object>> mukapathramDet = wardWiseERPrintWithoutPhotoService
					.getIndexPage(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("no_of_electors_data", no_of_electors_data);
			model.addAttribute("mukpathramDet", mukapathramDet);
			String partNos = "";
			int prtCnts = 0;
			for (Map<String, Object> mo : mukapathramDet) {
				model.addAttribute("revDivName", (String) mo.get("revenue_division_name"));
				model.addAttribute("assemblyId", (String) mo.get("assembly_id"));
				if (partNos.length() > 0 && !partNos.contains((String) mo.get("partno"))) {
					partNos += "," + (String) mo.get("partno");
					prtCnts = prtCnts + 1;
				} else if (partNos.length() < 1) {
					partNos = (String) mo.get("partno");
					prtCnts = 1;
				}
			}

			model.addAttribute("partNos", partNos);
			model.addAttribute("prtCnts", prtCnts);
			Calendar now = Calendar.getInstance();
			int year = now.get(Calendar.YEAR);
			String electionYear = String.valueOf(year);
			model.addAttribute("electionYear", electionYear);
			List<String> photos = new ArrayList<String>();

			if (data != null && data.size() > 0) {
				for (int i = 0; i < data.size(); i++) {
					m = (Map) data.get(i);
					if (m.get("photo") != null && !"null".equals(m.get("photo") + "")) {
						byte[] photoBytes = (byte[]) m.get("photo");
						String photo = Base64.getEncoder().encodeToString(photoBytes);
						photos.add(photo);
					} else {
						photos.add("NPA");
					}
					if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("M") || (m.get("sex") + "").trim().equals("m"))) {
						maleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").equals("F") || (m.get("sex") + "").equals("f"))) {
						femaleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("O") || (m.get("sex") + "").trim().equals("o"))) {
						otherVoter += 1;
					}
				}

				totalVoters = data.size();
				model.addAttribute("photos", photos);
				model.addAttribute("totalVoters", totalVoters);
				model.addAttribute("maleVoters", maleVoter);
				model.addAttribute("femaleVoters", femaleVoter);
				model.addAttribute("otherVoters", otherVoter);

				model.addAttribute("data", data);
				model.addAttribute("dataForPart", data);

				String dtStr = wardWiseERPrintWithoutPhotoService.dtStrWardWise(districtIdInt + "", mandalIdInt + "",
						gpcodeInt + "");
				String ageAsOnOld = "", publicationDateOld = "", sup1_from_dt = "", sup1_to_dt = "";

				if (!("0".equals(dtStr)) && !("").equals(dtStr)) {
					String temp[] = dtStr.split("##");
					ageAsOnOld = temp[0];
					publicationDateOld = temp[1];
					sup1_from_dt = temp[2];
					sup1_to_dt = temp[3];
				}

				model.addAttribute("age_as_on", ageAsOnOld);
				model.addAttribute("publication_date", publicationDateOld);
				model.addAttribute("sup1_from_dt", sup1_from_dt);
				model.addAttribute("sup1_to_dt", sup1_to_dt);
				List<Map<String, Object>> electorsDataWardWise = wardWiseERPrintWithoutPhotoService
						.getElectorsDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
				model.addAttribute("electorsDataWardWise", electorsDataWardWise);
				int noOfPages = (int) Math.ceil(data.size() / 21.0);
				noOfPages = noOfPages + 1;

				DecimalFormat df = new DecimalFormat("#");
				String roundedPages = df.format(noOfPages);

				model.addAttribute("noOfPages", roundedPages);
			}

			GPwiseVoterListRuralDTO gpWVLRuralDto1 = new GPwiseVoterListRuralDTO();
			model.addAttribute("gpWVLRuralDto1", gpWVLRuralDto1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "gpWardWiseWithPhotoWithCaste";
	}

	@GetMapping("viewEnglishWardWiseVotersWithoutPhoto")
	public String viewEnglishWardWiseVotersWithoutPhoto(@RequestParam("districtId") String districtId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("electionId") String electionId, HttpServletRequest request, Model model) {
		int totalVoters = 0, maleVoter = 0, femaleVoter = 0, otherVoter = 0;
		String eleDetails = null, distName = null, mandal_name = null, gpname = null;
		Map<String, Object> m = new HashMap<>();
		try {
			int districtIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(districtId)));
			int mandalIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(mandalId)));
			int gpcodeInt = Integer.parseInt(new String(Base64.getDecoder().decode(gpcode)));
			int electionIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(electionId)));

			model.addAttribute("electionId", electionIdInt);
			model.addAttribute("districtId", districtIdInt);
			model.addAttribute("mandalId", mandalIdInt);
			model.addAttribute("gpCode", gpcodeInt);
			model.addAttribute("wardId", 1);

			if (electionId != null && !"".equals(electionId.trim())) {
				eleDetails = userRepo.getElectionHeadingInt(electionIdInt + "");
			} else {
				eleDetails = " GP WISE  ELECTORAL  ROLLS  LIST ";
			}
			String signature = gpWVLRuralService.getSignature(districtIdInt + "");
			if (null != signature && !("".equals(signature))) {
				model.addAttribute("signature", signature);
			}

			distName = gpWVLRuralService.getDistName(districtIdInt + "");
			if (mandal_name == null) {
				Map<String, Object> mandalInt = psWiseSarpanchElectoralRollWithoutphotoService.mandalInt(districtIdInt,
						mandalIdInt);
				mandal_name = mandalInt.get("mandal_name") + "";
			} else {
				mandal_name = "--";
			}
			if (gpcode != null) {
				gpname = wardWiseERPrintWithoutPhotoService.getGPNameInt(districtIdInt, mandalIdInt, gpcodeInt)
						.get("gpname") + "";
			} else {
				gpname = "--";
			}
			model.addAttribute("eleDetails", eleDetails);
			model.addAttribute("distName", distName);
			model.addAttribute("mandal_name", mandal_name);
			model.addAttribute("gpname", gpname);

			model.addAttribute("dateTime", "      ");
			List<Map<String, Object>> assemblyList = wardWiseERPrintWithoutPhotoService
					.getAssemblyDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("assemblyList", assemblyList);
			model.addAttribute("assemblyName", assemblyList.get(0).get("assembly_name"));
			List<Map<String, Object>> no_of_electors_data = wardWiseERPrintWithoutPhotoService
					.getGpWaiseData(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");

			List<Map<String, Object>> data = wardWiseERPrintWithoutPhotoService.getVoterListWardWise(districtIdInt + "",
					mandalIdInt + "", gpcodeInt + "");
			List<Map<String, Object>> mukapathramDet = wardWiseERPrintWithoutPhotoService
					.getIndexPage(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("no_of_electors_data", no_of_electors_data);
			model.addAttribute("mukpathramDet", mukapathramDet);
			String partNos = "";
			int prtCnts = 0;
			for (Map<String, Object> mo : mukapathramDet) {
				model.addAttribute("revDivName", (String) mo.get("revenue_division_name"));
				model.addAttribute("assemblyId", (String) mo.get("assembly_id"));
				if (partNos.length() > 0 && !partNos.contains((String) mo.get("partno"))) {
					partNos += "," + (String) mo.get("partno");
					prtCnts = prtCnts + 1;
				} else if (partNos.length() < 1) {
					partNos = (String) mo.get("partno");
					prtCnts = 1;
				}
			}

			model.addAttribute("partNos", partNos);
			model.addAttribute("prtCnts", prtCnts);
			Calendar now = Calendar.getInstance();
			int year = now.get(Calendar.YEAR);
			String electionYear = String.valueOf(year);
			model.addAttribute("electionYear", electionYear);
			List<String> photos = new ArrayList<String>();
			if (data != null && data.size() > 0) {
				for (int i = 0; i < data.size(); i++) {
					m = (Map) data.get(i);
					if (m.get("photo") != null && !"null".equals(m.get("photo") + "")) {
						byte[] photoBytes = (byte[]) m.get("photo");
						String photo = Base64.getEncoder().encodeToString(photoBytes);
						photos.add(photo);
					} else {
						photos.add("NPA");
					}
					if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("M") || (m.get("sex") + "").trim().equals("m"))) {
						maleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").equals("F") || (m.get("sex") + "").equals("f"))) {
						femaleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("O") || (m.get("sex") + "").trim().equals("o"))) {
						otherVoter += 1;
					}
				}

				totalVoters = data.size();
				model.addAttribute("totalVoters", totalVoters);
				model.addAttribute("maleVoters", maleVoter);
				model.addAttribute("femaleVoters", femaleVoter);
				model.addAttribute("otherVoters", otherVoter);

				model.addAttribute("data", data);
				model.addAttribute("dataForPart", data);

				String dtStr = wardWiseERPrintWithoutPhotoService.dtStrWardWise(districtIdInt + "", mandalIdInt + "",
						gpcodeInt + "");
				String ageAsOnOld = "", publicationDateOld = "", sup1_from_dt = "", sup1_to_dt = "";
				List<Map<String, Object>> electorsDataWardWise = wardWiseERPrintWithoutPhotoService
						.getElectorsDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
				model.addAttribute("electorsDataWardWise", electorsDataWardWise);
				if (!("0".equals(dtStr)) && !("").equals(dtStr)) {
					String temp[] = dtStr.split("##");
					ageAsOnOld = temp[0];
					publicationDateOld = temp[1];
					sup1_from_dt = temp[2];
					sup1_to_dt = temp[3];
				}

				model.addAttribute("age_as_on", ageAsOnOld);
				model.addAttribute("publication_date", publicationDateOld);
				model.addAttribute("sup1_from_dt", sup1_from_dt);
				model.addAttribute("sup1_to_dt", sup1_to_dt);

				int noOfPages = (int) Math.ceil(data.size() / 21.0);
				noOfPages = noOfPages + 1;
				DecimalFormat df = new DecimalFormat("#");
				String roundedPages = df.format(noOfPages);
				model.addAttribute("noOfPages", roundedPages);
			}
			GPwiseVoterListRuralDTO gpWVLRuralDto1 = new GPwiseVoterListRuralDTO();
			model.addAttribute("gpWVLRuralDto1", gpWVLRuralDto1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "gpWardWiseEnglishWithoutPhotoReport";
	}

	@GetMapping("viewEnglishWardWiseVotersWithoutPhotoWithCaste")
	public String viewEnglishWardWiseVotersWithoutPhotoWithCaste(@RequestParam("districtId") String districtId,
			@RequestParam("mandalId") String mandalId, @RequestParam("gpcode") String gpcode,
			@RequestParam("electionId") String electionId, HttpServletRequest request, Model model) {
		int totalVoters = 0, maleVoter = 0, femaleVoter = 0, otherVoter = 0;
		String eleDetails = null, distName = null, mandal_name = null, gpname = null;
		Map<String, Object> m = new HashMap<>();
		try {
			int districtIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(districtId)));
			int mandalIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(mandalId)));
			int gpcodeInt = Integer.parseInt(new String(Base64.getDecoder().decode(gpcode)));
			int electionIdInt = Integer.parseInt(new String(Base64.getDecoder().decode(electionId)));

			model.addAttribute("electionId", electionIdInt);
			model.addAttribute("districtId", districtIdInt);
			model.addAttribute("mandalId", mandalIdInt);
			model.addAttribute("gpCode", gpcodeInt);
			model.addAttribute("wardId", 1);

			if (electionId != null && !"".equals(electionId.trim())) {
				eleDetails = userRepo.getElectionHeadingInt(electionIdInt + "");
			} else {
				eleDetails = " GP WISE  ELECTORAL  ROLLS  LIST ";
			}
			String signature = gpWVLRuralService.getSignature(districtIdInt + "");
			if (null != signature && !("".equals(signature))) {
				model.addAttribute("signature", signature);
			}

			distName = gpWVLRuralService.getDistName(districtIdInt + "");
			if (mandal_name == null) {
				Map<String, Object> mandalInt = psWiseSarpanchElectoralRollWithoutphotoService.mandalInt(districtIdInt,
						mandalIdInt);
				mandal_name = mandalInt.get("mandal_name") + "";
			} else {
				mandal_name = "--";
			}
			if (gpcode != null) {
				gpname = wardWiseERPrintWithoutPhotoService.getGPNameInt(districtIdInt, mandalIdInt, gpcodeInt)
						.get("gpname") + "";
			} else {
				gpname = "--";
			}
			model.addAttribute("eleDetails", eleDetails);
			model.addAttribute("distName", distName);
			model.addAttribute("mandal_name", mandal_name);
			model.addAttribute("gpname", gpname);

			model.addAttribute("dateTime", "      ");
			List<Map<String, Object>> assemblyList = wardWiseERPrintWithoutPhotoService
					.getAssemblyDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("assemblyList", assemblyList);
			model.addAttribute("assemblyName", assemblyList.get(0).get("assembly_name"));
			List<Map<String, Object>> no_of_electors_data = wardWiseERPrintWithoutPhotoService
					.getGpWaiseData(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");

			List<Map<String, Object>> data = wardWiseERPrintWithoutPhotoService.getVoterListWardWise(districtIdInt + "",
					mandalIdInt + "", gpcodeInt + "");
			List<Map<String, Object>> mukapathramDet = wardWiseERPrintWithoutPhotoService
					.getIndexPage(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
			model.addAttribute("no_of_electors_data", no_of_electors_data);
			model.addAttribute("mukpathramDet", mukapathramDet);
			String partNos = "";
			int prtCnts = 0;
			for (Map<String, Object> mo : mukapathramDet) {
				model.addAttribute("revDivName", (String) mo.get("revenue_division_name"));
				model.addAttribute("assemblyId", (String) mo.get("assembly_id"));
				if (partNos.length() > 0 && !partNos.contains((String) mo.get("partno"))) {
					partNos += "," + (String) mo.get("partno");
					prtCnts = prtCnts + 1;
				} else if (partNos.length() < 1) {
					partNos = (String) mo.get("partno");
					prtCnts = 1;
				}
			}

			model.addAttribute("partNos", partNos);
			model.addAttribute("prtCnts", prtCnts);
			Calendar now = Calendar.getInstance();
			int year = now.get(Calendar.YEAR);
			String electionYear = String.valueOf(year);
			model.addAttribute("electionYear", electionYear);
			List<String> photos = new ArrayList<String>();

			if (data != null && data.size() > 0) {
				for (int i = 0; i < data.size(); i++) {
					m = (Map) data.get(i);
					if (m.get("photo") != null && !"null".equals(m.get("photo") + "")) {
						byte[] photoBytes = (byte[]) m.get("photo");
						String photo = Base64.getEncoder().encodeToString(photoBytes);
						photos.add(photo);
					} else {
						photos.add("NPA");
					}
					if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("M") || (m.get("sex") + "").trim().equals("m"))) {
						maleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").equals("F") || (m.get("sex") + "").equals("f"))) {
						femaleVoter += 1;
					} else if (m.get("sex") != null
							&& ((m.get("sex") + "").trim().equals("O") || (m.get("sex") + "").trim().equals("o"))) {
						otherVoter += 1;
					}
				}

				totalVoters = data.size();
				model.addAttribute("totalVoters", totalVoters);
				model.addAttribute("maleVoters", maleVoter);
				model.addAttribute("femaleVoters", femaleVoter);
				model.addAttribute("otherVoters", otherVoter);

				model.addAttribute("data", data);
				model.addAttribute("dataForPart", data);
				List<Map<String, Object>> electorsDataWardWise = wardWiseERPrintWithoutPhotoService
						.getElectorsDataWardWise(districtIdInt + "", mandalIdInt + "", gpcodeInt + "");
				model.addAttribute("electorsDataWardWise", electorsDataWardWise);
				String dtStr = wardWiseERPrintWithoutPhotoService.dtStrWardWise(districtIdInt + "", mandalIdInt + "",
						gpcodeInt + "");
				String ageAsOnOld = "", publicationDateOld = "", sup1_from_dt = "", sup1_to_dt = "";

				if (!("0".equals(dtStr)) && !("").equals(dtStr)) {
					String temp[] = dtStr.split("##");
					ageAsOnOld = temp[0];
					publicationDateOld = temp[1];
					sup1_from_dt = temp[2];
					sup1_to_dt = temp[3];
				}

				model.addAttribute("age_as_on", ageAsOnOld);
				model.addAttribute("publication_date", publicationDateOld);
				model.addAttribute("sup1_from_dt", sup1_from_dt);
				model.addAttribute("sup1_to_dt", sup1_to_dt);

				int noOfPages = (int) Math.ceil(data.size() / 21.0);
				noOfPages = noOfPages + 1;

				DecimalFormat df = new DecimalFormat("#");
				String roundedPages = df.format(noOfPages);

				model.addAttribute("noOfPages", roundedPages);
			}

			GPwiseVoterListRuralDTO gpWVLRuralDto1 = new GPwiseVoterListRuralDTO();
			model.addAttribute("gpWVLRuralDto1", gpWVLRuralDto1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "gpWardWiseEnglishWithoutPhotoReportWithCaste";
	}
}