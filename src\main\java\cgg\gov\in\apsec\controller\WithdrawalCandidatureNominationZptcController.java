package cgg.gov.in.apsec.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cgg.gov.in.apsec.modal.User;
import cgg.gov.in.apsec.pojo.WithdrawalCandidatureNominationZptcDTO;
import cgg.gov.in.apsec.repo.UserRepository;
import cgg.gov.in.apsec.repo.WithdrawalCandidatureNominationZptcRepository;
import cgg.gov.in.apsec.service.ReservationForZptcROService;
import cgg.gov.in.apsec.service.WithdrawalCandidatureNominationZptcService;
import cgg.gov.in.apsec.utils.TokenUtil;

@Controller
public class WithdrawalCandidatureNominationZptcController {

	@Autowired
	private WithdrawalCandidatureNominationZptcService withdrawalCandidatureNominationZptcService;

	@Autowired
	private UserRepository userRepo;

	@Autowired
	private WithdrawalCandidatureNominationZptcRepository withdrawalCandidatureNominationZptcRepository;

	@Autowired
	private ReservationForZptcROService reservationForZptcROService;

	@RequestMapping(value = "/withdrawalCandidatureNominationForZptc")
	public String withdrawalCandidatureNominationForZptc(Model model, HttpServletRequest request,
			@ModelAttribute("withdrawalCandidatureNominationZptcDto") WithdrawalCandidatureNominationZptcDTO withdrawalCandidatureNominationZptcDto) {

		List<Map<String, Object>> withDrawalNominationList = null;
		List<Map<String, Object>> withDrawalStatusList = null;
		try {

			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			String districtId = user.getDistrict_id();
			String zppCode = user.getZppCode();

			Map<String, Object> zppData = reservationForZptcROService.getZppForROLogin(zppCode);
			model.addAttribute("zppData", zppData);

			List<Integer> districtIds = reservationForZptcROService.findDistrictIdByZppCode(Integer.parseInt(zppCode));
			String districtIdZpp = districtIds.get(0).toString();


			List<Map<String, Object>> districtList = reservationForZptcROService.findDistrictsByIds(districtIds);
			model.addAttribute("districtList", districtList);

			String zptcId = (String) model.asMap().get("zptcId");
			String assemblyId = (String) model.asMap().get("assemblyId");
			String revenueId = (String) model.asMap().get("revenueDivisionId");


			withdrawalCandidatureNominationZptcDto.setZptcId(zptcId);

			if (zptcId == null || zptcId.equals("0")) {
			} else {
				withDrawalNominationList = withdrawalCandidatureNominationZptcService
						.getwithdrawalNominationDataListZptc(districtIdZpp, revenueId,assemblyId, zptcId);
				model.addAttribute("withDrawalNominationList", withDrawalNominationList);

				withDrawalStatusList = withdrawalCandidatureNominationZptcService.getWithDrawaStatusListZptc(districtIdZpp,
						revenueId,assemblyId,zptcId);
				model.addAttribute("withDrawalStatusList", withDrawalStatusList);
				model.addAttribute("slectedZptcCode", zptcId);
			}


		} catch (Exception e) {
			e.printStackTrace();
		}
		return "withdrawalCandidatureNominationForZptc";
	}

	@PostMapping("/getDetailsForZptcWithDraw")
	public String getDetailsForZptcWithDraw(RedirectAttributes redirectAttr,
			@ModelAttribute("withdrawalCandidatureNominationZptcDto") WithdrawalCandidatureNominationZptcDTO withdrawalCandidatureNominationZptcDto) {
		try {
			redirectAttr.addFlashAttribute("zptcId", withdrawalCandidatureNominationZptcDto.getZptcId().split("_")[0]);
			redirectAttr.addFlashAttribute("revenueDivisionId", withdrawalCandidatureNominationZptcDto.getRevenueDivisionId());
			redirectAttr.addFlashAttribute("assemblyId", withdrawalCandidatureNominationZptcDto.getAssemblyId());
			List<String> zptcIds = reservationForZptcROService.getMandalIdsByZppDistRdIds(withdrawalCandidatureNominationZptcDto.getZppCode(), withdrawalCandidatureNominationZptcDto.getDistrictId(), withdrawalCandidatureNominationZptcDto.getRevenueDivisionId());
			List<Map<String,Object>> zptcDataRedir= new ArrayList<>();
			for (String string : zptcIds) {

				Map<String, Object> zptcData2 = reservationForZptcROService.getZptcData( withdrawalCandidatureNominationZptcDto.getDistrictId(), withdrawalCandidatureNominationZptcDto.getRevenueDivisionId(), string);
				if(!zptcData2.isEmpty())
					zptcDataRedir.add(zptcData2);
			}

			List<Map<String, Object>> acDataRedir=new ArrayList<>();


			acDataRedir = reservationForZptcROService.getACData(withdrawalCandidatureNominationZptcDto.getDistrictId(),  withdrawalCandidatureNominationZptcDto.getRevenueDivisionId());

			redirectAttr.addFlashAttribute("rdIdSelected", withdrawalCandidatureNominationZptcDto.getRevenueDivisionId()); 
			redirectAttr.addFlashAttribute("acCodeSelected", withdrawalCandidatureNominationZptcDto.getAssemblyId()); 
			redirectAttr.addFlashAttribute("zptcCodeSelected", withdrawalCandidatureNominationZptcDto.getZptcId()); 
			redirectAttr.addFlashAttribute("acDataRedir", acDataRedir); 
			redirectAttr.addFlashAttribute("zptcDataRedir", zptcDataRedir); 
			List<Map<String, Object>> gPsForProposerZptc = reservationForZptcROService.findGramPanchayatsForProposerZptc(Integer.parseInt(withdrawalCandidatureNominationZptcDto.getDistrictId()), Integer.parseInt(withdrawalCandidatureNominationZptcDto.getRevenueDivisionId()),  Integer.parseInt(withdrawalCandidatureNominationZptcDto.getZptcId().split("_")[0]));
			redirectAttr.addFlashAttribute("gPsForProposerZptc", gPsForProposerZptc); 

			redirectAttr.addFlashAttribute("districtIdSelected", withdrawalCandidatureNominationZptcDto.getDistrictId());

			List<String> distRdIds = reservationForZptcROService.getDistrictAndRevenueDivisionByZppCode(Integer.parseInt(withdrawalCandidatureNominationZptcDto.getZppCode()),Integer.parseInt(withdrawalCandidatureNominationZptcDto.getDistrictId()));

			List<Map<String, Object>> rdDataForRoZpp = reservationForZptcROService.rdDataForRoZpp(distRdIds);
			redirectAttr.addFlashAttribute("rdDataForRoZpp",rdDataForRoZpp);

			List<String> distZptcIds = reservationForZptcROService.findDistZptcIds(Integer.parseInt(withdrawalCandidatureNominationZptcDto.getZppCode()),Integer.parseInt(withdrawalCandidatureNominationZptcDto.getDistrictId()));

			List<Map<String, Object>> gpDataForRoZpp = reservationForZptcROService.getGPsForNominationsForRoZpp(distZptcIds);

			redirectAttr.addFlashAttribute("gpDataForRoZpp",gpDataForRoZpp);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return "redirect:/withdrawalCandidatureNominationForZptc";
	}
	
	@PostMapping("zptcwithdrawnStatus")
	@ResponseBody
	public ResponseEntity<Map<String, Object>> withdrawnStatus(
	        @ModelAttribute("withdrawalCandidatureNominationZptcDto") WithdrawalCandidatureNominationZptcDTO withdrawalCandidatureNominationZptcDto,
	        @RequestParam("slnoList") String slnoList,
	        @RequestParam("district_Id") String districtId,
	        @RequestParam("revenueDivision_Id") String revenueDivisionId,
	        @RequestParam("assembly_Id") String assemblyId,
	        @RequestParam("zptc_Id") String zptcId,
	        @RequestParam("processComplete") int processComplete) {

	    Map<String, Object> response = new HashMap<>();
	    try {
	        ObjectMapper objectMapper = new ObjectMapper();
	        List<Map<String, String>> slnoListMap = objectMapper.readValue(slnoList,
	                new TypeReference<List<Map<String, String>>>() {});
	        
	        int underscoreIndex = zptcId.indexOf('_');
	        String partBeforeUnderscore = zptcId.substring(0, underscoreIndex); 
	        zptcId = partBeforeUnderscore;

	        // Update withdrawal nominations
	        int result = withdrawalCandidatureNominationZptcService.updateZptcWithdrawalNomination(slnoListMap);
	        if (result > 0) {
	            response.put("success", true);
	            response.put("message", "Withdrawal process completed successfully.");
	        } else {
	            response.put("success", false);
	            response.put("message", "Failed to complete the withdrawal process.");
	            return ResponseEntity.ok(response);
	        }

	        // Check if data is already confirmed
	        int existingCount = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcConfirmCountAjax(
	                districtId, revenueDivisionId, assemblyId, zptcId);
	        if (existingCount > 0) {
	            response.put("success", false);
	            response.put("message", "Data already confirmed!");
	            return ResponseEntity.ok(response);
	        }

	        // Process completion confirmation
	        int roCount = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcConfirmByROAjax(
	                districtId, revenueDivisionId, assemblyId, zptcId, processComplete);
	        if (roCount > 0) {
	            response.put("success", true);
	            response.put("message", "Process completed and data updated.");
	        } else {
	            response.put("success", false);
	            response.put("message", "No data updated during process completion.");
	        }
	    } catch (Exception e) {
	        e.printStackTrace();
	        response.put("success", false);
	        response.put("message", "An error occurred during processing: " + e.getMessage());
	    }

	    return ResponseEntity.ok(response);
	}
	
	@RequestMapping(value = "/receiptZptcAcknowledged")
	public String receiptZptcAcknowledged(Model model, @RequestParam("slno") String slno) {
		try {
			model.addAttribute("slno", slno);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "receiptAcknowledgedforZptc";
	}
	
	@RequestMapping(value = "/reportAKWZptcData")
	 public ResponseEntity<String> reportACKFormZptcData(Model model, 
			 @ModelAttribute("withdrawalCandidatureNominationZptcDto") WithdrawalCandidatureNominationZptcDTO withdrawalCandidatureNominationZptcDto,            
       RedirectAttributes redirectAttr,HttpServletRequest request,
         @RequestParam("akwzptccandidateSigned") String akwzptccandidateSigned,
         @RequestParam("akwbywhomezptcformSubmite") String akwbywhomezptcformSubmite,
         @RequestParam("dateFieldOne") String dateFieldOne,
         @RequestParam("timeFieldTwo") String timeFieldTwo,
         @RequestParam("slno") Integer slno) {	     
	     try {
	    	 String userId = request.getSession().getAttribute("loggedInUser").toString();
	    	 WithdrawalCandidatureNominationZptcDTO dto=new WithdrawalCandidatureNominationZptcDTO();
	    	 dto.setAkwzptccandidateSigned(akwzptccandidateSigned);
	    	 dto.setAkwbywhomezptcformSubmite(akwbywhomezptcformSubmite);
	    	 dto.setDateFieldOne(dateFieldOne);
	    	 dto.setTimeFieldTwo(timeFieldTwo);
			int result = withdrawalCandidatureNominationZptcService
					.insertreportAKWFormInfo(withdrawalCandidatureNominationZptcDto, userId, slno.toString(),akwzptccandidateSigned,akwbywhomezptcformSubmite);
			if (result > 0) {
				redirectAttr.addFlashAttribute("saveSuccess", " ReportAcknowlwdged Data Saved Successfully...!!!");
			} else {
				redirectAttr.addFlashAttribute("saveFailure", "Failed ReportAcknowlwdged Data ...!!!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	     return ResponseEntity.ok("Success");
	}
	
	@RequestMapping(value = "/fetchZptcFormAKWInfo")
	public String fetchZptcFormAKWInfo(HttpServletRequest request ,Model model, @RequestParam("slno") String slno) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String zppCode = user.getZppCode();
			model.addAttribute("zptccodeName",withdrawalCandidatureNominationZptcRepository.getZppForROLogin(zppCode));
			
			String ZptcName = withdrawalCandidatureNominationZptcRepository.akwZptcCodeName(slno);
			model.addAttribute("ZptcName",ZptcName);
			
			String  akwzptcCandidateName =  withdrawalCandidatureNominationZptcService.akwZptcName(slno);
	    	model.addAttribute("akwzptcCandidateName", akwzptcCandidateName);
	    	
	    	String  akwcandidateDate =  withdrawalCandidatureNominationZptcService.akwDate(slno);
	    	model.addAttribute("akwcandidateDate", akwcandidateDate);
	    	
	    	String  akwcandidateTime =  withdrawalCandidatureNominationZptcService.akwTime(slno);
	    	model.addAttribute("akwcandidateTime", akwcandidateTime);
	    	
	    	String akwbywhomeformSubmit = withdrawalCandidatureNominationZptcService.akwbywhomeformSubmit(slno);
			model.addAttribute("akwbywhomeformSubmit", akwbywhomeformSubmit);
	        String whomSubmitted="";
			if(akwbywhomeformSubmit.equals("1")) {
				whomSubmitted=	akwzptcCandidateName;
			}
			if(akwbywhomeformSubmit.equals("2")) {
				whomSubmitted=withdrawalCandidatureNominationZptcRepository.whomSubmitted_proposer_zptc(slno);
			}
			if(akwbywhomeformSubmit.equals("3")) {
				whomSubmitted=userRepo.whomSubmitted_election_agent_ward(user.getDistrict_id(),user.getMandalId(), user.getGpcode());
			}
			model.addAttribute("whomSubmitted",whomSubmitted);
			
			String roUploadsignature = userRepo.getZptcROUploadsignature(userId);
			model.addAttribute("roUploadsignature", roUploadsignature);
	    	
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchFormforZptcReportAcknowledgeTel";
	}
	
	@RequestMapping(value = "/getWithdrawlZptcConfirmCountAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlZptcConfirmCountAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "assemblyId") String assemblyId,  @RequestParam(value = "zptcId") String zptcId) {
		int count = 0;
		try { 
			 int underscoreIndex = zptcId.indexOf('_');
		        String partBeforeUnderscore = zptcId.substring(0, underscoreIndex); 
		        zptcId = partBeforeUnderscore;
		        
			count = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcConfirmCountAjax1(districtId, revenueDivisionId, assemblyId,zptcId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	@RequestMapping(value = "/getWithdrawlZptcConfirmByROAjax", method = RequestMethod.POST)
	public @ResponseBody int getWithdrawlZptcConfirmByROAjax(@RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "assemblyId") String assemblyId,  @RequestParam(value = "zptcId") String zptcId,
			@RequestParam(value = "processComplete") int processComplete) {
		int count = 0;
		try {
			 int underscoreIndex = zptcId.indexOf('_');
		        String partBeforeUnderscore = zptcId.substring(0, underscoreIndex); 
		        zptcId = partBeforeUnderscore;
				if(processComplete == 1) {
					count = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcConfirmByROAjax1(districtId, revenueDivisionId, assemblyId,zptcId, processComplete);
				}else if(processComplete == 2){
					int processCount = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcProcessCount(districtId, revenueDivisionId, assemblyId,zptcId);
					if(processCount > 0) {
						int updateCount = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcConfirmByROAjax1(districtId, revenueDivisionId, assemblyId,zptcId, processComplete);
						if(updateCount > 0) {
							count = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcDraftCount(districtId, revenueDivisionId, assemblyId,zptcId);
						}	
					}
				}else if(processComplete == 3){
					int draftCount =  withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcDraftCount(districtId, revenueDivisionId, assemblyId, zptcId);
					if(draftCount > 0) {
						count = withdrawalCandidatureNominationZptcRepository.getWithdrawlZptcConfirmByROAjax1(districtId, revenueDivisionId, assemblyId, zptcId, processComplete);
					}
				}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	
	@RequestMapping(value = "/fetchForm8ZptcData", method = RequestMethod.GET)
	public String fetchForm8ZptcData(HttpServletRequest request, @RequestParam(value = "districtId") String districtId, @RequestParam(value = "revenueDivisionId") String revenueDivisionId,
			@RequestParam(value = "assemblyId") String assemblyId,  @RequestParam(value = "zptcId") String zptcId, Model model) {
		try {
			String userId = request.getSession().getAttribute("loggedInUser").toString();
			User user = userRepo.getDetailsByUserId(userId);
			String token = TokenUtil.generateToken(request.getSession());
			model.addAttribute("formToken", token);
			int underscoreIndex = zptcId.indexOf('_');
			String partBeforeUnderscore = zptcId.substring(0, underscoreIndex); 
			zptcId = partBeforeUnderscore;

			String zppCode = user.getZppCode();
			model.addAttribute("zptccodeName",withdrawalCandidatureNominationZptcRepository.getZppForROLogin(zppCode));

			String MandalName = withdrawalCandidatureNominationZptcRepository.getMandalName(districtId, revenueDivisionId, zptcId);
			model.addAttribute("MandalName",MandalName);
			
			List<Map<String, Object>> formeightData =
					withdrawalCandidatureNominationZptcRepository.getFormVIIImptcdatanew(districtId, revenueDivisionId, assemblyId,zptcId);

//			String roUploadsignature = userRepo.getZptcROUploadsignature(userId);
//			model.addAttribute("roUploadsignature", roUploadsignature);

			model.addAttribute("formeightDataList", formeightData);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "fetchFormVIIIZptcforTelugu";
	}
}
